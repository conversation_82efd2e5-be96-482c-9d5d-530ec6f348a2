/**
 * 主入口文件
 * 负责初始化所有模块，并处理页面加载和消息通信
 */
(function() {
    'use strict';
    
    console.log('内容脚本已加载');

    /**
     * 初始化所有功能模块
     */
    function initializeInterface() {
        Utils.log('主入口', '开始初始化界面');
        
        // 分析当前页面
        const currentUrl = window.location.href;
        // 修改URL匹配规则，使其同时支持新建页面和重新发布页面
        const isTargetPublishPage = currentUrl.includes('ses.zzds888.com:1655/Task/AddTask');
        
        // 创建UI管理器并初始化 - 始终初始化，UI管理器内部会判断是否显示AI面板
        const uiManager = new UIManager();
        uiManager.initialize();
        
        // 只在发布页面初始化AI功能
        if (isTargetPublishPage) {
            Utils.log('主入口', '检测到发布页面，初始化AI功能');
            const aiManager = new AIFeatureManager();
            aiManager.initialize();
        }
        
        // 初始化粘贴管理器（如果需要）
        if (window.PasteAreaManager) {
            const pasteManager = new PasteAreaManager();
            pasteManager.initializePasteAreas();
            Utils.log('主脚本', '粘贴管理器已初始化');
        }
        
        // 初始化 SYCM 处理器
        if (window.SycmHandler) {
            const sycmHandler = new SycmHandler();
            sycmHandler.initialize();
            Utils.log('主脚本', 'SYCM 处理器已初始化');
        }
        
        // 页面分析
        analyzePageStructure();
        
        // 延迟检查是否需要确保AI功能初始化成功
        if (isTargetPublishPage) {
            setTimeout(checkAIInitialization, 3000);
        }
        
        Utils.log('主入口', '界面初始化完成');
    }
    
    /**
     * 分析页面结构
     */
    function analyzePageStructure() {
        Utils.log('主入口', '执行页面结构分析');
        
        // 检测当前页面类型
        const currentUrl = window.location.href;
        
        if (currentUrl.includes('zzds888.com')) {
            Utils.log('主入口', '检测到网站A页面');
            // TODO: 特定于网站A的初始化
        } else if (AppConfig.SITES.TAOBAO.URL_PATTERN.test(currentUrl)) {
            Utils.log('主入口', '检测到淘宝/天猫页面');
            // TODO: 特定于淘宝的初始化
        }
    }
    
    /**
     * 检查AI功能是否成功初始化
     */
    function checkAIInitialization() {
        Utils.log('主入口', '检查AI功能是否正确初始化');
        // 可以在这里添加额外的检查和修复逻辑
    }
    
    /**
     * 处理接收到的消息
     */
    function handleMessage(message, sender, sendResponse) {
        if (message.type === 'startProcessing') {
            Utils.log('主入口', '开始处理图片组，共' + message.reviews.length + '组');
            
            sendResponse({ received: true });
            
            // TODO: 委托给相应的处理器处理
            return true;
        } else if (message.type === 'stopProcessing') {
            Utils.log('主入口', '停止处理');
            
            // TODO: 停止处理
            sendResponse({ received: true });
        }
        return true;
    }
    
    // 确保在页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeInterface);
    } else {
        initializeInterface();
    }
    
    // 监听来自后台脚本的消息
    chrome.runtime.onMessage.addListener(handleMessage);
})();
