/**
 * 评论管理器
 * 负责加载、管理和提供评论文本
 */
window.CommentManager = class CommentManager {
    constructor() {
        this.comments = []; // 存储所有评论
        this.usedComments = new Set(); // 存储已使用的评论索引
        this.isRandom = true; // 是否启用随机评论
        this.commentsUrl = chrome.runtime.getURL('pingyu.txt'); // 评论文件路径
        this.isInitialized = false;
    }

    /**
     * 初始化评论管理器，加载评论文件
     */
    async initialize() {
        if (this.isInitialized) return;
        Utils.log('评论管理', '初始化评论管理器');
        try {
            const response = await fetch(this.commentsUrl);
            if (!response.ok) {
                throw new Error(`获取评论文件失败: ${response.statusText}`);
            }
            const text = await response.text();
            this.comments = text.split('\n')
                               .map(line => line.trim())
                               .filter(line => line.length > 0); // 去除空行

            if (this.comments.length === 0) {
                 Utils.log('评论管理', '评论文件为空或加载失败', 'warn');
                 // 可以提供一些默认评论
                 this.comments = ['好评！', '质量不错', '值得推荐', '下次还来', '满意'];
            } else {
                 Utils.log('评论管理', `成功加载 ${this.comments.length} 条评论`);
            }

            // 从 storage 加载已使用评论和随机状态
            const storedUsed = localStorage.getItem('usedComments');
            if (storedUsed) {
                this.usedComments = new Set(JSON.parse(storedUsed));
            }
            const storedRandom = localStorage.getItem('isRandomComment');
            if (storedRandom !== null) {
                this.isRandom = JSON.parse(storedRandom);
            }

            this.isInitialized = true;
        } catch (error) {
            console.error('初始化评论管理器失败:', error);
            Utils.log('评论管理', `初始化失败: ${error.message}`, 'error');
            // 使用默认评论
            this.comments = ['好评！', '质量不错'];
            this.isInitialized = true; // 即使失败也标记为初始化，避免阻塞
        }
    }

    /**
     * 获取一条随机或顺序的评论
     * @returns {string} 一条评论文本
     */
    async getRandomComment() {
        if (!this.isInitialized) {
            await this.initialize(); // 确保已初始化
        }
        if (this.comments.length === 0) return '好评！'; // 兜底评论

        if (this.usedComments.size >= this.comments.length) {
            Utils.log('评论管理', '所有评论已使用，重置使用记录');
            this.usedComments.clear(); // 如果所有评论都用完了，重置
        }

        let index;
        let attempts = 0;
        const maxAttempts = this.comments.length * 2; // 防止无限循环

        do {
            if (this.isRandom) {
                index = Math.floor(Math.random() * this.comments.length);
            } else {
                // 顺序获取下一个未使用的
                index = -1;
                for(let i = 0; i < this.comments.length; i++) {
                    if (!this.usedComments.has(i)) {
                        index = i;
                        break;
                    }
                }
                // 如果顺序找不到未使用的（理论上在重置逻辑下不太可能），随机选一个
                if (index === -1) {
                     index = Math.floor(Math.random() * this.comments.length);
                     Utils.log('评论管理', '顺序获取失败，回退到随机', 'warn');
                }
            }
            attempts++;
        } while (this.usedComments.has(index) && attempts < maxAttempts); // 循环直到找到未使用的或达到最大尝试次数

        if (attempts >= maxAttempts) {
             Utils.log('评论管理', '无法找到未使用的评论，可能存在问题', 'warn');
             // 随机返回一个，避免卡死
             index = Math.floor(Math.random() * this.comments.length);
        }

        this.usedComments.add(index);
        // 保存已使用评论到 storage
        localStorage.setItem('usedComments', JSON.stringify(Array.from(this.usedComments)));

        Utils.log('评论管理', `提供评论 (索引: ${index}, 随机: ${this.isRandom})`);
        return this.comments[index];
    }

    /**
     * 重置已使用的评论记录
     */
    reset() {
        this.usedComments.clear();
        localStorage.removeItem('usedComments');
        Utils.log('评论管理', '已重置评论使用记录');
    }

    /**
     * 切换随机评论状态
     * @returns {boolean} 当前是否启用随机评论
     */
    toggleRandom() {
        this.isRandom = !this.isRandom;
        localStorage.setItem('isRandomComment', JSON.stringify(this.isRandom));
        Utils.log('评论管理', `随机评论已切换为: ${this.isRandom}`);
        return this.isRandom;
    }

    /**
     * 检查是否启用了随机评论
     * @returns {boolean}
     */
    isRandomEnabled() {
        // 确保从localStorage加载最新状态
         const storedRandom = localStorage.getItem('isRandomComment');
         if (storedRandom !== null) {
             this.isRandom = JSON.parse(storedRandom);
         }
        return this.isRandom;
    }
}; 