/**
 * UI管理模块
 * 负责创建和管理界面控件，如控制面板、测试按钮等
 */

// UI管理器类
window.UIManager = class UIManager {
    constructor() {
        this.initialized = false;
    }

    /**
     * 初始化UI管理器
     */
    initialize() {
        if (this.initialized) {
            Utils.log('UI管理', 'UI管理器已初始化');
            return;
        }

        // 创建强制测试按钮，但仅在网站A的发布页面
        const currentUrl = window.location.href;
        if (currentUrl.includes('ses.zzds888.com:1655/Task/AddTask') || currentUrl.includes('order.sanmato.com:1677/Task/AddTask')) {
            // 先延迟一点执行自动解析URL
            setTimeout(() => {
                this.autoParseAllTaobaoUrls();

                // 设置观察器以监控内容变化
                this.setupUrlObserver();

                // 添加生意参谋跳转按钮
                this.addSycmJumpButtons();
            }, 1500);

            // 再延迟创建UI面板
            setTimeout(() => {
                this.createForceTestButton();
            }, 2000);
            Utils.log('UI管理', '在发布页面创建AI面板');
        } else {
            Utils.log('UI管理', '当前不是发布页面，不创建AI面板');
        }

        this.initialized = true;
        Utils.log('UI管理', 'UI管理器初始化完成');
    }

    /**
     * 设置监控URL变化的观察器
     */
    setupUrlObserver() {
        Utils.log('URL解析', '设置URL监控观察器');

        try {
            // 记录上次检查时间，防止短时间内多次触发
            let lastCheckTime = 0;
            const MIN_CHECK_INTERVAL = 500; // 最小检查间隔(毫秒)

            // 创建节流函数，避免短时间内频繁触发处理逻辑
            const throttledCheck = () => {
                const now = Date.now();
                // 如果距离上次检查时间不足最小间隔，则跳过本次检查
                if (now - lastCheckTime < MIN_CHECK_INTERVAL) return;

                lastCheckTime = now;
                this.autoParseAllTaobaoUrls();
                this.addSycmJumpButtons();
                Utils.log('URL解析', '执行URL检查 - 节流控制');
            };

            // 创建MutationObserver来监控DOM变化
            const observer = new MutationObserver((mutations) => {
                // 标记是否需要检查
                let needToCheck = false;

                // 跳过某些已知会频繁变化但不相关的元素
                const skipSelectors = [
                    '#force-ai-test-status', // 状态显示元素
                    '.sycm-jump-btn',       // 我们自己添加的按钮
                    '#direct-ai-result'      // AI结果容器
                ];

                mutations:
                for (const mutation of mutations) {
                    // 跳过对不相关元素的变化处理
                    for (const selector of skipSelectors) {
                        if (mutation.target.matches && mutation.target.matches(selector)) {
                            continue mutations;
                        }
                    }

                    // 如果是输入框值变化
                    if (mutation.type === 'attributes' &&
                        mutation.attributeName === 'value' &&
                        mutation.target.tagName === 'INPUT' &&
                        mutation.target.type === 'text') {

                        // 验证值是否与淘宝链接相关
                        const value = mutation.target.value || '';
                        if (value.includes('taobao.com') ||
                            value.includes('tmall.com') ||
                            value.includes('item.htm')) {
                            needToCheck = true;
                            break;
                        }
                    }

                    // 如果有新节点添加
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否添加了与淘宝链接相关的输入框
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType !== Node.ELEMENT_NODE) continue;

                            // 直接检查添加的节点是否是输入框
                            if (node.tagName === 'INPUT' && node.type === 'text') {
                                const value = node.value || '';
                                if (value.includes('taobao.com') ||
                                    value.includes('tmall.com') ||
                                    value.includes('item.htm')) {
                                    needToCheck = true;
                                    break;
                                }
                            }

                            // 如果添加的是容器元素，检查其中是否有包含淘宝链接的输入框
                            const inputs = node.querySelectorAll('input[type="text"]');
                            for (const input of inputs) {
                                const value = input.value || '';
                                if (value.includes('taobao.com') ||
                                    value.includes('tmall.com') ||
                                    value.includes('item.htm')) {
                                    needToCheck = true;
                                    break;
                                }
                            }

                            if (needToCheck) break;
                        }
                    }

                    if (needToCheck) break;
                }

                // 如果需要检查，则使用节流函数处理
                if (needToCheck) {
                    Utils.log('URL解析', '检测到淘宝链接相关变化');
                    throttledCheck();
                }
            });

            // 配置观察器 - 只监控相关的变化
            const observerConfig = {
                attributes: true,
                childList: true,
                subtree: true,
                attributeFilter: ['value'] // 只关注value属性变化
            };

            // 开始观察整个文档
            observer.observe(document.body, observerConfig);
            Utils.log('URL解析', 'URL监控观察器已启动');

            // 作为额外保障，使用更长的间隔进行定期检查
            let lastIntervalCheckTime = 0;
            setInterval(() => {
                const now = Date.now();
                // 距离上次检查至少5秒才执行
                if (now - lastIntervalCheckTime >= 5000) {
                    lastIntervalCheckTime = now;
                    this.autoParseAllTaobaoUrls();
                    this.addSycmJumpButtons();
                    Utils.log('URL解析', '执行URL定期检查');
                }
            }, 5000);
        } catch (error) {
            Utils.log('URL解析', `设置URL监控观察器失败: ${error.message}`);
        }
    }

    /**
     * 创建测试按钮
     */
    createForceTestButton() {
        // 如果已经存在，则不重复创建
        if (document.getElementById('force-ai-test-button')) {
            return;
        }

        // 获取AI设置
        const aiSettings = Utils.getAISettings();

        // 创建主容器
        const container = Utils.createElement('div', {
            id: 'force-ai-test-container',
            style: AppConfig.STYLES.CONTROL_PANEL
        });

        // 创建标题
        const title = Utils.createElement('div', {
            style: `
                font-weight: bold;
                font-size: 16px;
                margin-bottom: 15px;
                color: #333;
                border-bottom: 1px solid #eee;
                padding-bottom: 8px;
                text-align: center;
            `
        }, 'AI 关键词生成');
        container.appendChild(title);

        // 创建模型输入框（而不是下拉框）
        const modelLabel = Utils.createElement('div', {
            style: 'margin-bottom: 5px; font-size: 14px;'
        }, '模型:');
        container.appendChild(modelLabel);

        const modelInput = Utils.createElement('input', {
            id: 'ai-model-input',
            type: 'text',
            value: aiSettings.model,
            placeholder: '例如: Qwen/QwQ-32B-Preview',
            style: `
                width: 100%;
                padding: 5px;
                margin-bottom: 10px;
                border: 1px solid #ddd;
                border-radius: 3px;
                box-sizing: border-box;
            `
        });
        container.appendChild(modelInput);

        // 添加常用模型提示
        const modelTip = Utils.createElement('div', {
            style: `
                font-size: 12px;
                color: #666;
                margin-bottom: 15px;
            `
        }, '推荐: Qwen/QwQ-32B-Preview, anthropic/claude-3-5-sonnet');
        container.appendChild(modelTip);

        // 创建高级设置折叠区域
        const advancedSettingsToggle = Utils.createElement('div', {
            id: 'advanced-settings-toggle',
            style: `
                cursor: pointer;
                margin-bottom: 10px;
                font-size: 14px;
                color: #1E9FFF;
                user-select: none;
            `
        }, '▶ 显示高级设置', {
            click: function() {
                const advancedSettings = document.getElementById('advanced-settings-content');
                const isHidden = advancedSettings.style.display === 'none';

                if (isHidden) {
                    advancedSettings.style.display = 'block';
                    this.textContent = '▼ 隐藏高级设置';
                } else {
                    advancedSettings.style.display = 'none';
                    this.textContent = '▶ 显示高级设置';
                }
            }
        });
        container.appendChild(advancedSettingsToggle);

        // 创建高级设置内容区域（默认隐藏）
        const advancedSettings = Utils.createElement('div', {
            id: 'advanced-settings-content',
            style: `
                display: none;
                padding: 10px;
                background: #f8f8f8;
                border-radius: 4px;
                margin-bottom: 15px;
            `
        });

        // 创建API键输入框
        advancedSettings.appendChild(Utils.createElement('div', {
            style: 'margin-bottom: 5px; font-size: 14px;'
        }, 'API Key:'));

        advancedSettings.appendChild(Utils.createElement('input', {
            id: 'ai-api-key',
            type: 'password', // 使用password类型增加安全性
            value: aiSettings.apiKey,
            placeholder: '输入你的API Key',
            style: `
                width: 100%;
                padding: 5px;
                margin-bottom: 10px;
                border: 1px solid #ddd;
                border-radius: 3px;
                box-sizing: border-box;
            `
        }));

        // 创建API URL输入框
        advancedSettings.appendChild(Utils.createElement('div', {
            style: 'margin-bottom: 5px; font-size: 14px;'
        }, 'API URL:'));

        advancedSettings.appendChild(Utils.createElement('input', {
            id: 'ai-api-url',
            type: 'text',
            value: aiSettings.apiUrl,
            placeholder: 'API URL',
            style: `
                width: 100%;
                padding: 5px;
                margin-bottom: 10px;
                border: 1px solid #ddd;
                border-radius: 3px;
                box-sizing: border-box;
            `
        }));

        container.appendChild(advancedSettings);

        // 创建保存设置按钮
        const saveButton = Utils.createElement('button', {
            id: 'save-ai-settings',
            style: `
                padding: 5px 12px;
                background: #009688;
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
                margin-right: 5px;
                margin-bottom: 15px;
                width: 100%;
            `
        }, '保存设置', {
            click: this.saveAISettingsHandler
        });

        container.appendChild(saveButton);

        // 创建测试按钮
        const button = Utils.createElement('button', {
            id: 'force-ai-test-button',
            style: `
                padding: 8px 16px;
                background: #1E9FFF;
                color: white;
                font-size: 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                width: 100%;
                margin-bottom: 10px;
            `
        }, '执行AI分析', {
            click: function() {
                window.directAnalyzeTitle();
            }
        });

        container.appendChild(button);

        // 添加商品信息轮换按钮
        const rotateButton = Utils.createElement('button', {
            id: 'rotate-goods-info-button',
            style: `
                padding: 8px 16px;
                background: #FF5722;
                color: white;
                font-size: 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                width: 100%;
                margin-bottom: 10px;
            `
        }, '轮换商品信息', {
            click: this.rotateGoodsInfoHandler.bind(this)
        });

        container.appendChild(rotateButton);

        // 添加淘宝链接解析按钮
        const parseUrlButton = Utils.createElement('button', {
            id: 'parse-taobao-url-button',
            style: `
                padding: 8px 16px;
                background: #FF9800;
                color: white;
                font-size: 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                width: 100%;
                margin-bottom: 10px;
            `
        }, '解析淘宝链接', {
            click: this.parseTaobaoUrlsHandler.bind(this)
        });

        container.appendChild(parseUrlButton);

        // 创建分隔线
        const divider = Utils.createElement('div', {
            style: `
                height: 1px;
                background: #eee;
                margin: 10px 0;
                width: 100%;
            `
        });
        container.appendChild(divider);

        // 创建状态显示
        const status = Utils.createElement('div', {
            id: 'force-ai-test-status',
            style: `
                margin-top: 10px;
                font-size: 14px;
                color: #666;
                text-align: center;
            `
        }, '状态: 等待点击');
        container.appendChild(status);

        // 添加到文档
        document.body.appendChild(container);

        Utils.log('UI管理', '创建了AI测试按钮');
    }

    /**
     * 保存AI设置的处理函数
     */
    saveAISettingsHandler() {
        const model = document.getElementById('ai-model-input').value;
        const apiKey = document.getElementById('ai-api-key').value;
        const apiUrl = document.getElementById('ai-api-url').value;

        // 验证输入
        if (!model) {
            alert('请填写模型名称');
            return;
        }

        if (!apiKey) {
            alert('请填写API Key');
            return;
        }

        if (!apiUrl) {
            alert('请填写API URL');
            return;
        }

        // 保存设置
        const settings = { model, apiKey, apiUrl };
        if (Utils.saveAISettings(settings)) {
            // 更新状态
            const statusDiv = document.getElementById('force-ai-test-status');
            if (statusDiv) {
                statusDiv.textContent = '状态: 设置已保存';
                statusDiv.style.color = 'green';

                // 2秒后恢复状态
                setTimeout(() => {
                    const statusDiv = document.getElementById('force-ai-test-status');
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 等待点击';
                        statusDiv.style.color = '#666';
                    }
                }, 2000);
            }
        }
    }

    /**
     * 商品信息轮换处理函数
     */
    rotateGoodsInfoHandler() {
        try {
            Utils.log('UI管理', '开始轮换商品信息');
            const statusDiv = document.getElementById('force-ai-test-status');
            if (statusDiv) {
                statusDiv.textContent = '状态: 轮换中...';
                statusDiv.style.color = '#1E9FFF';
            }

            // 查找所有商品信息区块
            const goodsInfoBlocks = [];
            let index = 1;

            // 查找商品信息区块，直到找不到为止
            while (true) {
                const blockSelector = `商品信息${index}`;
                const blocks = Array.from(document.querySelectorAll('legend, .strong')).filter(el =>
                    el.textContent.includes(blockSelector)
                );

                if (blocks.length === 0) {
                    // 如果找不到更多区块，则退出循环
                    break;
                }

                // 记录区块
                goodsInfoBlocks.push(blocks[0]);
                index++;
            }

            if (goodsInfoBlocks.length <= 1) {
                // 如果只有一个商品信息区块，无需轮换
                Utils.log('UI管理', '找到的商品信息区块数量不足，无法轮换');
                if (statusDiv) {
                    statusDiv.textContent = '状态: 商品数量不足，无法轮换';
                    statusDiv.style.color = 'red';
                }
                setTimeout(() => {
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 等待点击';
                        statusDiv.style.color = '#666';
                    }
                }, 2000);
                return;
            }

            Utils.log('UI管理', `找到${goodsInfoBlocks.length}个商品信息区块`);

            // 收集每个区块的商品链接、成交价和展示价格
            const goodsData = [];
            for (let i = 0; i < goodsInfoBlocks.length; i++) {
                const block = goodsInfoBlocks[i];

                // 获取包含此区块的大容器
                const container = this.findParentContainer(block);
                if (!container) {
                    Utils.log('UI管理', `无法找到区块${i+1}的父容器`);
                    continue;
                }

                // 查找链接输入框
                let linkInput = null;
                const possibleLinkLabels = Array.from(container.querySelectorAll('label')).filter(label =>
                    label.textContent.includes('商品链接') || label.textContent.includes('链接')
                );

                if (possibleLinkLabels.length > 0) {
                    // 找到了标签，查找其相关的输入框
                    for (const label of possibleLinkLabels) {
                        const formItem = label.closest('.layui-form-item');
                        if (formItem) {
                            const foundInput = formItem.querySelector('input[type="text"]');
                            if (foundInput) {
                                linkInput = foundInput;
                                break;
                            }
                        }
                    }
                }

                // 备用方法：如果没找到，使用URL值作为线索
                if (!linkInput) {
                    const allInputs = container.querySelectorAll('input[type="text"]');
                    for (const input of allInputs) {
                        if (input.value && (
                            input.value.includes('taobao.com') ||
                            input.value.includes('tmall.com') ||
                            input.value.includes('item.htm')
                        )) {
                            linkInput = input;
                            break;
                        }
                    }
                }

                // 查找成交价输入框
                let priceInput = null;
                const possiblePriceLabels = Array.from(container.querySelectorAll('label')).filter(label =>
                    label.textContent.includes('成交价')
                );

                if (possiblePriceLabels.length > 0) {
                    for (const label of possiblePriceLabels) {
                        const formItem = label.closest('.layui-form-item');
                        if (formItem) {
                            const foundInput = formItem.querySelector('input[type="text"]');
                            if (foundInput) {
                                priceInput = foundInput;
                                break;
                            }
                        }
                    }
                }

                // 如果没找到，尝试查找数字输入框
                if (!priceInput) {
                    const allInputs = container.querySelectorAll('input[type="text"]');
                    for (const input of allInputs) {
                        // 如果输入的是纯数字，可能是价格
                        if (input.value && /^\d+$/.test(input.value.trim())) {
                            // 跳过已经找到的链接输入框
                            if (input === linkInput) continue;

                            priceInput = input;
                            break;
                        }
                    }
                }

                // 查找展示价格输入框
                let displayPriceInput = null;
                const possibleDisplayPriceLabels = Array.from(container.querySelectorAll('label')).filter(label =>
                    label.textContent.includes('展示价格')
                );

                if (possibleDisplayPriceLabels.length > 0) {
                    for (const label of possibleDisplayPriceLabels) {
                        const formItem = label.closest('.layui-form-item');
                        if (formItem) {
                            const foundInput = formItem.querySelector('input[type="text"]');
                            if (foundInput) {
                                displayPriceInput = foundInput;
                                break;
                            }
                        }
                    }
                }

                // 如果还没找到，找另一个数字输入框
                if (!displayPriceInput) {
                    const allInputs = container.querySelectorAll('input[type="text"]');
                    for (const input of allInputs) {
                        // 如果输入的是纯数字，并且不是成交价输入框，可能是展示价格
                        if (input.value && /^\d+$/.test(input.value.trim())) {
                            // 跳过已经找到的输入框
                            if (input === linkInput || input === priceInput) continue;

                            displayPriceInput = input;
                            break;
                        }
                    }
                }

                // 如果找到了所有必要的输入框，则记录数据
                if (linkInput && priceInput && displayPriceInput) {
                    // 对淘宝链接进行解析和简化
                    const originalLink = linkInput.value;
                    const parsedLink = this.parseTaobaoUrl(originalLink);

                    // 如果URL成功解析，立即更新输入框
                    if (parsedLink !== originalLink) {
                        linkInput.value = parsedLink;
                        this.triggerInputEvent(linkInput);
                        Utils.log('UI管理', `原始链接已简化: ${originalLink.substring(0, 20)}... -> ${parsedLink}`);
                    }

                    goodsData.push({
                        link: parsedLink, // 使用解析后的链接
                        price: priceInput.value,
                        displayPrice: displayPriceInput.value,
                        linkInput,
                        priceInput,
                        displayPriceInput
                    });

                    Utils.log('UI管理', `商品${i+1}数据: 链接=${parsedLink.substring(0, 50)}..., 成交价=${priceInput.value}, 展示价格=${displayPriceInput.value}`);
                } else {
                    Utils.log('UI管理', `区块${i+1}缺少必要的输入框: 链接=${!!linkInput}, 成交价=${!!priceInput}, 展示价格=${!!displayPriceInput}`);
                }
            }

            if (goodsData.length <= 1) {
                Utils.log('UI管理', '有效商品数据不足，无法轮换');
                if (statusDiv) {
                    statusDiv.textContent = '状态: 有效商品数据不足，无法轮换';
                    statusDiv.style.color = 'red';
                }
                setTimeout(() => {
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 等待点击';
                        statusDiv.style.color = '#666';
                    }
                }, 2000);
                return;
            }

            // 执行轮换逻辑：将第一个商品信息放到最后，其他商品信息前移
            const firstData = goodsData[0];

            // 轮换数据
            for (let i = 0; i < goodsData.length - 1; i++) {
                const currentData = goodsData[i];
                const nextData = goodsData[i + 1];

                // 将下一个商品的数据填充到当前商品位置
                currentData.linkInput.value = nextData.link;
                currentData.priceInput.value = nextData.price;
                currentData.displayPriceInput.value = nextData.displayPrice;

                // 触发事件，确保表单验证和其他脚本能感知到变化
                this.triggerInputEvent(currentData.linkInput);
                this.triggerInputEvent(currentData.priceInput);
                this.triggerInputEvent(currentData.displayPriceInput);
            }

            // 将第一个商品的数据填充到最后一个商品位置
            const lastData = goodsData[goodsData.length - 1];
            lastData.linkInput.value = firstData.link;
            lastData.priceInput.value = firstData.price;
            lastData.displayPriceInput.value = firstData.displayPrice;

            this.triggerInputEvent(lastData.linkInput);
            this.triggerInputEvent(lastData.priceInput);
            this.triggerInputEvent(lastData.displayPriceInput);

            Utils.log('UI管理', '商品信息轮换完成');

            // 更新状态
            if (statusDiv) {
                statusDiv.textContent = '状态: 轮换完成';
                statusDiv.style.color = 'green';
                setTimeout(() => {
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 等待点击';
                        statusDiv.style.color = '#666';
                    }
                }, 2000);
            }
        } catch (error) {
            Utils.log('UI管理', `轮换商品信息出错: ${error.message}`);
            const statusDiv = document.getElementById('force-ai-test-status');
            if (statusDiv) {
                statusDiv.textContent = `状态: 轮换失败 - ${error.message}`;
                statusDiv.style.color = 'red';
            }
        }
    }

    /**
     * 查找包含商品信息的父容器
     * @param {Element} element - 开始查找的元素
     * @returns {Element} 父容器元素或null
     */
    findParentContainer(element) {
        // 向上找5层，找到可能的父容器
        let current = element;
        for (let i = 0; i < 5; i++) {
            if (!current || current === document.body) {
                return null;
            }

            if (current.tagName === 'FIELDSET' ||
                current.classList.contains('layui-field') ||
                current.classList.contains('layui-elem-field')) {
                return current;
            }

            current = current.parentElement;
        }

        return current;
    }

    /**
     * 触发input事件，以便其他脚本感知到值的变化
     * @param {Element} input - 输入元素
     */
    triggerInputEvent(input) {
        if (!input) return;

        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
    }

    /**
     * 解析淘宝URL，提取商品ID并返回标准化URL
     * @param {string} url - 原始淘宝URL
     * @returns {string} - 简化后的URL
     */
    parseTaobaoUrl(url) {
        if (!url) return url;

        try {
            // 调用 Utils 中的方法
            const itemId = Utils.extractTaobaoItemId(url);

            // 如果成功提取到ID，返回标准化URL
            if (itemId) {
                Utils.log('URL解析', `提取到商品ID: ${itemId}`);
                return `https://item.taobao.com/item.htm?id=${itemId}`;
            }

            Utils.log('URL解析', '无法提取商品ID');
            return url;
        } catch (error) {
            Utils.log('URL解析', `解析URL出错: ${error.message}`);
            return url;
        }
    }

    /**
     * 处理页面上所有淘宝链接的解析
     */
    parseTaobaoUrlsHandler() {
        try {
            Utils.log('URL解析', '开始解析页面上的淘宝链接');
            const statusDiv = document.getElementById('force-ai-test-status');
            if (statusDiv) {
                statusDiv.textContent = '状态: 正在解析链接...';
                statusDiv.style.color = '#1E9FFF';
            }

            // 查找所有可能包含淘宝链接的输入框
            const allTextInputs = document.querySelectorAll('input[type="text"]');
            let parsedCount = 0;

            for (const input of allTextInputs) {
                const originalValue = input.value;

                // 检查是否是淘宝链接
                if (originalValue && (
                    originalValue.includes('taobao.com') ||
                    originalValue.includes('tmall.com') ||
                    originalValue.includes('item.htm')
                )) {
                    // 解析链接
                    const parsedLink = this.parseTaobaoUrl(originalValue);

                    // 如果解析结果与原始值不同，更新输入框
                    if (parsedLink !== originalValue) {
                        input.value = parsedLink;
                        this.triggerInputEvent(input);
                        parsedCount++;
                        Utils.log('URL解析', `链接已简化: ${originalValue.substring(0, 20)}... -> ${parsedLink}`);
                    }
                }
            }

            // 更新状态
            if (statusDiv) {
                if (parsedCount > 0) {
                    statusDiv.textContent = `状态: 已简化 ${parsedCount} 个链接`;
                    statusDiv.style.color = 'green';
                } else {
                    statusDiv.textContent = '状态: 未找到需要简化的链接';
                    statusDiv.style.color = '#666';
                }

                setTimeout(() => {
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 等待点击';
                        statusDiv.style.color = '#666';
                    }
                }, 3000);
            }

            Utils.log('URL解析', `完成解析，共简化 ${parsedCount} 个链接`);
        } catch (error) {
            Utils.log('URL解析', `解析链接出错: ${error.message}`);
            const statusDiv = document.getElementById('force-ai-test-status');
            if (statusDiv) {
                statusDiv.textContent = `状态: 解析失败 - ${error.message}`;
                statusDiv.style.color = 'red';
            }
        }
    }

    /**
     * 自动解析页面上所有淘宝链接
     */
    autoParseAllTaobaoUrls() {
        // 简单调用一次解析处理函数
        this.parseTaobaoUrlsHandler();
    }

    /**
     * 显示用户提示消息
     * @param {string} message - 提示消息
     * @param {string} type - 消息类型: 'success', 'info', 'warning', 'error'
     * @param {number} duration - 持续时间(毫秒)
     * @private
     */
    _showNotification(message, type = 'info', duration = 3000) {
        try {
            // 检查是否已存在通知容器
            let notifyContainer = document.getElementById('ui-manager-notification');
            if (!notifyContainer) {
                // 创建通知容器
                notifyContainer = Utils.createElement('div', {
                    id: 'ui-manager-notification',
                    style: `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        max-width: 350px;
                        font-size: 14px;
                    `
                });
                document.body.appendChild(notifyContainer);
            }

            // 根据类型设置样式
            let bgColor, textColor, borderColor;
            switch (type) {
                case 'success':
                    bgColor = '#eeffee';
                    textColor = '#009900';
                    borderColor = '#55cc55';
                    break;
                case 'warning':
                    bgColor = '#ffffee';
                    textColor = '#cc8800';
                    borderColor = '#ffcc44';
                    break;
                case 'error':
                    bgColor = '#ffeeee';
                    textColor = '#cc0000';
                    borderColor = '#ff5555';
                    break;
                default: // info
                    bgColor = '#eef6ff';
                    textColor = '#0066cc';
                    borderColor = '#66aaff';
            }

            // 创建通知元素
            const notification = Utils.createElement('div', {
                style: `
                    padding: 12px 16px;
                    margin-bottom: 10px;
                    background-color: ${bgColor};
                    color: ${textColor};
                    border-left: 4px solid ${borderColor};
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    border-radius: 4px;
                    opacity: 0;
                    transition: opacity 0.3s ease-in-out;
                `
            }, message);

            // 添加到容器
            notifyContainer.appendChild(notification);

            // 渐入效果
            setTimeout(() => {
                notification.style.opacity = '1';
            }, 10);

            // 设置自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, duration);

        } catch (error) {
            console.error('显示通知失败:', error);
        }
    }

    /**
     * 处理错误并显示适当的提示
     * @param {Error} error - 错误对象
     * @param {string} action - 正在执行的操作描述
     * @param {boolean} showToUser - 是否向用户显示错误
     * @private
     */
    _handleError(error, action, showToUser = true) {
        // 记录错误到控制台和日志
        console.error(`${action}发生错误:`, error);
        Utils.log('UI管理', `${action}失败: ${error.message}`, 'error');

        // 如果需要，显示给用户
        if (showToUser) {
            this._showNotification(`${action}失败: ${error.message}`, 'error');
        }

        // 更新状态显示
        const statusDiv = document.getElementById('force-ai-test-status');
        if (statusDiv) {
            statusDiv.textContent = `状态: ${action}失败`;
            statusDiv.style.color = 'red';

            // 2秒后恢复
            setTimeout(() => {
                if (statusDiv) {
                    statusDiv.textContent = '状态: 等待点击';
                    statusDiv.style.color = '#666';
                }
            }, 2000);
        }
    }

    /**
     * 创建通用的控制面板 (基础结构)
     * 具体按钮和功能由调用者添加
     * @returns {Element} - 控制面板容器元素
     */
    createBaseControlPanel() {
        Utils.log('UI管理', '创建基础控制面板');
        const panel = Utils.createElement('div', {
            id: 'extension-control-panel', // 使用通用ID
            style: AppConfig.STYLES.CONTROL_PANEL // 使用config中的样式
        });
        // 可以添加一个标题或图标
        // panel.innerHTML = '<h4>扩展控制面板</h4>';
        return panel;
    }

    /**
     * 为网站A创建并显示控制面板
     * 包含 AI 分析按钮、设置按钮等
     */
    createSiteAControlPanel() {
        Utils.log('UI管理', '创建网站A控制面板');
        if (document.getElementById('site-a-control-panel')) {
            Utils.log('UI管理', '网站A控制面板已存在');
            return;
        }

        const controlPanel = Utils.createElement('div', {
            id: 'site-a-control-panel',
            style: AppConfig.STYLES.CONTROL_PANEL_SITE_A // 使用网站A特定样式
        });

        // 1. AI分析按钮 (直接触发分析)
        const analyzeButton = Utils.createElement('button', {
            id: 'direct-ai-analyze-btn',
            style: AppConfig.STYLES.BUTTON_PRIMARY + 'margin-right: 8px;',
            content: '✨ AI生成关键词'
        }, null, {
            click: () => {
                if (typeof directAnalyzeTitle === 'function') {
                     Utils.log('UI管理', '点击 AI分析按钮');
                     directAnalyzeTitle();
                } else {
                     console.error('directAnalyzeTitle 函数未定义');
                     alert('AI分析功能初始化失败');
                }
            }
        });
        controlPanel.appendChild(analyzeButton);

        // 2. 显示/隐藏高级设置按钮
        const settingsButton = Utils.createElement('button', {
            id: 'toggle-ai-settings-btn',
            style: AppConfig.STYLES.BUTTON_SECONDARY,
            content: '⚙️ 高级设置'
        }, null, {
            click: () => this.toggleAISettingsPanel()
        });
        controlPanel.appendChild(settingsButton);

        // 3. AI设置面板 (默认隐藏)
        const settingsPanel = this.createAISettingsPanel();
        controlPanel.appendChild(settingsPanel);

        // 4. AI 分析状态指示器 (可选)
        const statusIndicator = Utils.createElement('div', {
             id: 'ai-direct-analysis-status',
             style: 'margin-left: 15px; font-size: 12px; color: #888;'
        });
        controlPanel.appendChild(statusIndicator);

        // 将控制面板添加到页面合适的位置
        // 尝试添加到页面顶部或表单上方
        const targetContainer = document.querySelector(AppConfig.SELECTORS.SITE_A.PANEL_TARGET_CONTAINER) || document.body;
        // 确保添加到主文档，而不是iframe内部 (如果面板总是在主文档显示)
        document.body.insertBefore(controlPanel, document.body.firstChild);
        // targetContainer.insertBefore(controlPanel, targetContainer.firstChild);
        Utils.log('UI管理', '网站A控制面板已添加到页面');
    }

    /**
     * 创建AI设置面板
     * @returns {Element} 设置面板元素
     */
    createAISettingsPanel() {
        Utils.log('UI管理', '创建AI设置面板');
        const settingsPanel = Utils.createElement('div', {
            id: 'ai-settings-panel',
            style: AppConfig.STYLES.AI_SETTINGS_PANEL
        });

        const currentSettings = Utils.getAISettings();

        settingsPanel.innerHTML = `
            <h4>AI分析高级设置</h4>
            <div class="setting-item">
                <label for="ai-api-url">API URL:</label>
                <input type="text" id="ai-api-url" value="${currentSettings.apiUrl}">
            </div>
            <div class="setting-item">
                <label for="ai-model">模型名称:</label>
                <input type="text" id="ai-model" value="${currentSettings.model}">
            </div>
            <div class="setting-item">
                <label for="ai-api-key">API Key:</label>
                <input type="password" id="ai-api-key" value="${currentSettings.apiKey}">
            </div>
            <div style="margin-top: 15px; text-align: right;">
                <button id="save-ai-settings-btn" style="${AppConfig.STYLES.BUTTON_SAVE}">保存设置</button>
                <button id="cancel-ai-settings-btn" style="${AppConfig.STYLES.BUTTON_CANCEL} margin-left: 5px;">取消</button>
            </div>
        `;

        // 添加保存按钮事件
        const saveBtn = settingsPanel.querySelector('#save-ai-settings-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                 Utils.log('UI管理', '点击 保存AI设置');
                 const newSettings = {
                     apiUrl: settingsPanel.querySelector('#ai-api-url').value.trim(),
                     model: settingsPanel.querySelector('#ai-model').value.trim(),
                     apiKey: settingsPanel.querySelector('#ai-api-key').value.trim()
                 };

                 if (!newSettings.apiUrl || !newSettings.model || !newSettings.apiKey) {
                     alert('请填写所有AI设置项！');
                     return;
                 }

                 const success = Utils.saveAISettings(newSettings);
                 if (success) {
                     alert('AI设置已保存！');
                     this.toggleAISettingsPanel(); // 关闭面板
                 } else {
                      alert('保存AI设置失败，请检查浏览器存储权限或联系管理员。');
                 }
            });
        }

        // 添加取消按钮事件
        const cancelBtn = settingsPanel.querySelector('#cancel-ai-settings-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                 Utils.log('UI管理', '点击 取消AI设置');
                 this.toggleAISettingsPanel();
             });
        }

        return settingsPanel;
    }

    /**
     * 切换AI设置面板的显示状态
     */
    toggleAISettingsPanel() {
        const settingsPanel = document.getElementById('ai-settings-panel');
        if (settingsPanel) {
            const isVisible = settingsPanel.style.display === 'block';
            settingsPanel.style.display = isVisible ? 'none' : 'block';
            Utils.log('UI管理', `AI设置面板已 ${isVisible ? '隐藏' : '显示'}`);
             // 如果显示面板，重新加载当前设置
             if (!isVisible) {
                 const currentSettings = Utils.getAISettings();
                 settingsPanel.querySelector('#ai-api-url').value = currentSettings.apiUrl;
                 settingsPanel.querySelector('#ai-model').value = currentSettings.model;
                 settingsPanel.querySelector('#ai-api-key').value = currentSettings.apiKey;
             }
        }
    }

    /**
     * 更新图片上传状态的UI显示
     * @param {number} current - 当前处理的组号 (从1开始)
     * @param {number} total - 总组数
     * @param {string} statusText - 状态描述文本
     */
    updateUploadStatus(current, total, statusText) {
        Utils.log('UI管理 (上传状态)', `进度: ${current}/${total} - ${statusText}`);
        let statusDiv = document.getElementById('image-upload-status-indicator');

        if (!statusDiv) {
            statusDiv = Utils.createElement('div', {
                id: 'image-upload-status-indicator',
                style: AppConfig.STYLES.STATUS_INDICATOR // 使用配置的样式
            });
            document.body.appendChild(statusDiv);
        }

        statusDiv.textContent = `图片上传: ${statusText} (${current > 0 ? current + '/' + total : '--'})`;
        statusDiv.style.display = 'block'; // 确保可见

        // 根据状态更新样式
        if (statusText.includes('完成') || statusText.includes('成功')) {
            statusDiv.className = 'status-indicator success';
        } else if (statusText.includes('失败') || statusText.includes('错误') || statusText.includes('中断')) {
            statusDiv.className = 'status-indicator error';
        } else if (statusText.includes('处理中') || statusText.includes('处理组') || statusText.includes('开始')) {
            statusDiv.className = 'status-indicator processing';
        } else {
            statusDiv.className = 'status-indicator info'; // 默认信息样式
        }

        // 设置一段时间后自动隐藏 (可选)
        clearTimeout(this.statusHideTimer); // 清除之前的计时器
        if (statusText.includes('完成') || statusText.includes('失败') || statusText.includes('错误') || statusText.includes('中断')) {
            this.statusHideTimer = setTimeout(() => {
                 if (statusDiv) statusDiv.style.display = 'none';
            }, 5000); // 5秒后隐藏最终状态
        }
    }

    // ... 其他 UI 管理方法 ...

};
