# 淘宝晒图转存与网站A数据填充助手 - 注意事项

本文档旨在提供使用此Chrome扩展的注意事项、配置说明和基本维护指南。

## 一、核心功能

1.  **淘宝评论晒图转存:**
    *   在淘宝订单详情页的评价管理区域，自动为买家晒图添加复选框。
    *   提供 "批量复制" 和 "批量转存" 按钮。
    *   "批量复制" 将选中图片的URL复制到剪贴板。
    *   "批量转存" 将选中图片信息发送到后台，用于后续在网站A进行填充。
    *   **支持自动下载功能：** 可配置为在转存时自动将图片下载到本地指定文件夹。
2.  **网站A数据自动填充:**
    *   在网站A的商品发布/编辑页面，读取后台转存的图片数据。
    *   根据配置，自动将图片URL、价格、评论等信息填充到对应的表单字段中。
    *   提供 "粘贴板智能填充" 功能，监控剪贴板内容，自动识别并填充淘宝链接及相关信息。
    *   **支持图片自动上传：** 可配置为自动将本地图片上传到网站A的图片空间。
3.  **淘宝链接快速跳转与分析:**
    *   在网站A的页面上，自动识别淘宝商品链接。
    *   在链接旁添加 "生意参谋" 快捷跳转按钮，方便快速查看商品数据。
    *   提供 "一键解析链接" 功能，尝试从各种格式的淘宝链接中提取商品ID。
4.  **AI 辅助功能 (网站A):**
    *   提供 "AI分析标题" 功能，调用配置的AI模型API，根据商品标题生成推荐的搜索关键词。
    *   自动将生成的关键词填充到网站A的对应输入框中。

## 二、文件结构

```
.
├── background/                 # 后台脚本目录
│   └── background.js           # 主要后台逻辑 (消息处理, 存储, 下载等)
├── content-scripts/            # 内容脚本目录 (注入到页面)
│   ├── ai-feature.js           # AI分析标题相关功能
│   ├── comment-manager.js      # 评论管理 (获取/存储评论)
│   ├── data-filler.js          # 网站A数据填充逻辑
│   ├── image-uploader.js       # 网站A图片上传逻辑
│   ├── paste-manager.js        # 粘贴板监控与处理
│   ├── taobao-image-collector.js # 淘宝图片选择与收集逻辑
│   ├── taobao-link-handler.js  # 淘宝链接处理 (网站A)
│   └── ui-manager.js           # 网站A的UI交互管理 (按钮添加等)
├── modules/                    # 通用模块或库
│   ├── app-config.js           # 应用配置 (选择器, URL模式, API密钥等)
│   └── utils.js                # 通用辅助函数 (日志, DOM操作, 存储等)
├── .cursor/                    # Cursor IDE 特定配置 (可忽略)
├── background.js               # 旧版后台脚本(可能已废弃或整合) -> **注意：此文件似乎与background/background.js重复，需确认哪个是主文件**
├── manifest.json               # 扩展清单文件 (权限, 配置)
├── pingyu.txt                  # 默认评论数据文件
├── site-a-content.js           # 网站A主要内容脚本入口 (可能已整合或废弃) -> **需确认**
├── taobao-content.js           # 淘宝主要内容脚本入口 (可能已整合或废弃) -> **需确认**
├── zhiyi-content.js            # 另一个网站(指引?)的内容脚本 -> **需确认用途**
└── 注意事项.md                 # 本文档
```

**重要说明:**
*   请确认 `background.js`, `site-a-content.js`, `taobao-content.js` 是否为主入口文件或已被 `background/` 和 `content-scripts/` 中的文件替代。
*   请确认 `zhiyi-content.js` 的具体用途和是否仍在使用。

## 三、安装与配置

1.  **安装:**
    *   打开 Chrome 浏览器，进入 `chrome://extensions/`。
    *   开启右上角的 "开发者模式"。
    *   点击 "加载已解压的扩展程序"，选择本扩展的根目录。
2.  **核心配置 (`modules/app-config.js`):**
    *   **`DEBUG`**: 是否开启调试模式 (在控制台输出更多日志)。
    *   **`API_KEYS`**: 配置AI分析功能所需的API密钥和模型信息 (如 OpenAI, Gemini 等)。**请务必保护好您的 API Key！**
    *   **`SITES`**: 配置目标网站的 URL 模式、选择器等信息。
        *   **`SITE_A`**: 网站A (数据填充目标网站) 的配置。包含 URL 匹配模式、各种表单元素 (标题、URL、价格、评论、关键词输入框)、上传按钮、成功/错误指示器等的 CSS 选择器。**这是扩展正常工作的关键，如果网站A页面结构更新，需要及时修改这里的选择器。**
        *   **`TAOBAO`**: 淘宝网的配置。包含订单详情页、评论管理区域、图片元素等的选择器。
    *   **`SELECTORS`**: 集中管理所有用到的 CSS 选择器。
    *   **`SETTINGS_DEFAULTS`**: 扩展设置的默认值，如是否自动下载、下载路径、是否启用粘贴板监控等。
    *   **`AI`**: AI 功能相关配置，如 API 端点、模型名称、提示词模板、最大 Token 数等。
    *   **`STYLES`**: 扩展添加的 UI 元素的样式。
3.  **扩展设置 (通过扩展图标访问):**
    *   **自动下载图片:** 转存时是否自动下载图片到本地。
    *   **下载路径:** 图片默认下载到的子目录名称 (相对于浏览器默认下载目录)。
    *   **启用粘贴板监控:** 是否自动监控剪贴板并尝试填充数据。
    *   **评论模式:** 选择评论填充的方式 (顺序、随机、禁用)。
    *   **显示高级设置 (AI配置):** 允许用户在界面上临时修改 AI API Key、模型名称和 API URL (不建议频繁修改，优先修改 `app-config.js`)。

## 四、使用注意事项

1.  **选择器依赖:**
    *   **核心风险:** 本扩展高度依赖淘宝网和网站A的页面HTML结构。**如果淘宝或网站A更新页面，导致CSS选择器失效，扩展相关功能将无法正常工作** (例如：找不到图片、无法添加按钮、无法填充数据等)。
    *   **应对:** 定期检查扩展功能是否正常。如果失效，需要更新 `modules/app-config.js` 中的 `SELECTORS` 和 `SITES` 配置。建议熟悉浏览器开发者工具 (F12) 以便查找正确的选择器。
2.  **淘宝评论加载:**
    *   淘宝的评论/晒图区域可能是动态加载的。扩展会尝试使用 `MutationObserver` 来检测并处理新加载的内容。如果加载方式改变，可能需要调整 `content-scripts/taobao-image-collector.js` 中的监控逻辑。
3.  **网站A图片上传:**
    *   图片上传功能模拟了用户操作，但网站A的上传机制 (如接口、验证方式) 如果发生变化，可能导致上传失败。
    *   上传状态的判断依赖特定的成功/失败提示元素，如果提示方式改变，上传流程会卡住。
4.  **数据填充:**
    *   填充逻辑依赖于表单元素的稳定存在。如果网站A修改了表单结构 (如增删字段、修改ID/类名)，填充会失败。
    *   价格、评论等数据的准确性依赖于淘宝页面提供的信息。
5.  **AI 功能:**
    *   需要正确配置 API Key 和模型名称。错误的 Key 或无效的模型会导致请求失败。
    *   API 调用受网络状况和目标服务可用性的影响。
    *   生成的关键词质量取决于 AI 模型的能力和提示词的设计。
    *   API 调用可能产生费用，请注意您的 API 服务商的计费规则。
6.  **评论数据 (`pingyu.txt`):**
    *   扩展会读取此文件作为评论来源。确保文件使用 UTF-8 编码，每行一条评论。
    *   如果文件不存在或读取失败，会使用内置的几条默认评论。
7.  **权限:**
    *   扩展需要 "storage", "clipboardRead", "scripting", "downloads", "activeTab" 以及对目标网站的访问权限。请确保在安装或更新时授予这些权限。
    *   "clipboardRead" 权限用于读取剪贴板，请注意隐私风险。
8.  **浏览器兼容性:**
    *   主要在最新版 Chrome 浏览器上开发和测试。其他 Chromium 内核浏览器 (如 Edge) 理论上兼容，但未经充分测试。
9.  **与其他扩展的冲突:**
    *   某些操作 DOM 或修改页面行为的扩展可能与本扩展冲突。如果遇到问题，尝试禁用其他扩展进行排查。
10. **数据安全与隐私:**
    *   API Key 存储在 `app-config.js` 中，请勿将包含敏感 Key 的代码公开分享。
    *   剪贴板读取功能涉及用户数据，请确保用户知情。
    *   扩展不收集或上传任何用户个人身份信息。

## 五、维护与调试

1.  **查看日志:**
    *   在扩展管理页面 (`chrome://extensions/`) 找到本扩展，点击 "背景页" (或类似名称) 打开后台脚本的控制台。
    *   在目标网站页面按 F12 打开开发者工具，查看 "Console" 面板，可以看到内容脚本的日志 (需要开启 `DEBUG` 模式或手动添加 `console.log`)。
2.  **更新选择器:**
    *   当功能失效时，最常见的原因是选择器过期。
    *   使用开发者工具检查目标元素，获取新的 ID、class 或其他属性，更新 `app-config.js`。
3.  **检查配置:**
    *   确认 `manifest.json` 中的权限、内容脚本匹配规则是否正确。
    *   确认 `app-config.js` 中的 API Key、URL 模式等配置是否正确。
4.  **网络请求:**
    *   在开发者工具的 "Network" 面板检查 AI API 调用、图片加载等网络请求是否成功。

## 六、常见问题 (FAQ)

*   **Q: 淘宝页面没有出现复选框和转存按钮?**
    *   A: 检查淘宝选择器配置 (`app-config.js`) 是否失效；检查扩展是否被禁用或权限不足；查看控制台是否有错误日志。
*   **Q: 网站A没有自动填充数据?**
    *   A: 检查网站A选择器配置是否失效；确认之前是否成功在淘宝转存了数据；检查后台脚本和内容脚本的控制台是否有错误；确认填充的目标表单是否已加载。
*   **Q: 图片上传失败?**
    *   A: 检查网站A的上传相关选择器配置；检查网站A的图片空间是否已满或接口变更；检查网络连接。
*   **Q: AI分析失败?**
    *   A: 检查API Key和模型配置；检查网络连接；查看AI服务商的状态；查看控制台错误详情。
*   **Q: 粘贴板监控无效?**
    *   A: 确认扩展设置中已启用该功能；确认扩展拥有 "clipboardRead" 权限；检查控制台错误。

---
*文档更新时间: <由系统自动生成或手动填写>*