// 添加启动日志
console.log('Service Worker 启动');

// 处理图片URL
function processImageUrl(url) {
    if (!url) return '';
    
    console.log('processImageUrl - 处理前URL:', url);
    
    let processedUrl = url;
    
    // 添加协议（如果缺少）
    processedUrl = processedUrl.replace(/^\/\//, 'https://');
    
    // 移除查询参数
    processedUrl = processedUrl.replace(/\?.*$/, '');
    
    // 专门处理淘宝图片中常见的尺寸格式: _960x960.jpg 
    processedUrl = processedUrl.replace(/\_\d+x\d+\.jpg/g, '');
    
    // 处理类似 _180x180.jpg_960x960.jpg_.webp 这样的多重尺寸后缀
    processedUrl = processedUrl.replace(/\_\d+x\d+\.jpg\_\d+x\d+\.jpg/g, '');
    
    // 处理 .jpg_.webp 格式
    processedUrl = processedUrl.replace(/\.jpg_\.webp/g, '.jpg');
    
    // 处理 .jpg.jpg 双重后缀
    processedUrl = processedUrl.replace(/\.jpg\.jpg/g, '.jpg');
    
    // 提取第一个有效扩展名
    const baseUrlMatch = processedUrl.match(/^(.*?)\.(jpe?g|png|gif|webp)/i);
    if (baseUrlMatch) {
        // 使用基础URL和第一个扩展名重构URL
        processedUrl = `${baseUrlMatch[1]}.${baseUrlMatch[2]}`;
    }
    
    // 兜底替换任何剩余的特殊格式
    processedUrl = processedUrl.replace(/\_\.webp$/i, '');
    processedUrl = processedUrl.replace(/\_\d+x\d+/g, '');
    
    console.log('processImageUrl - 处理后URL:', processedUrl);
    
    return processedUrl;
}

// 消息处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // 处理评论转移
    if (message.type === 'processReviews') {
        console.log('收到处理请求:', message);
        
        if (message.action === 'checkTab') {
            // 处理URL和价格填充请求
            chrome.tabs.query({
                url: 'https://ses.zzds888.com:1655/*'
            }, function(tabs) {
                let targetTab;
                
                if (tabs.length > 0) {
                    // 使用现有标签页
                    targetTab = tabs[0];
                    console.log('找到现有网站A标签页:', targetTab.id);
                    
                    // 激活标签页
                    chrome.tabs.update(targetTab.id, { active: true }, function() {
                        // 发送数据填充消息
                        chrome.tabs.sendMessage(targetTab.id, {
                            type: 'fillData',
                            data: message.data
                        });
                    });
                } else {
                    // 创建新标签页
                    chrome.tabs.create({
                        url: 'https://ses.zzds888.com:1655/Task/AddTask'
                    }, function(tab) {
                        targetTab = tab;
                        console.log('创建新标签页:', targetTab.id);
                        
                        // 等待页面加载完成后发送消息
                        const listener = (tabId, changeInfo) => {
                            if (tabId === tab.id && changeInfo.status === 'complete') {
                                setTimeout(() => {
                                    chrome.tabs.sendMessage(tab.id, {
                                        type: 'fillData',
                                        data: message.data
                                    });
                                }, 1000);
                                
                                // 移除监听器
                                chrome.tabs.onUpdated.removeListener(listener);
                            }
                        };
                        chrome.tabs.onUpdated.addListener(listener);
                    });
                }
            });
            return true;
        }
        
        // 处理评论转移
        if (message.reviews) {
            console.log('收到处理评论请求:', message.reviews.length, '组');
            
            // 查找现有的网站A标签页
            chrome.tabs.query({
                url: 'https://ses.zzds888.com:1655/*'
            }, function(tabs) {
                let targetTab;
                
                if (tabs.length > 0) {
                    // 使用现有标签页
                    targetTab = tabs[0];
                    console.log('找到现有网站A标签页:', targetTab.id);
                    
                    // 激活标签页
                    chrome.tabs.update(targetTab.id, { active: true }, function() {
                        // 预处理所有图片URL
                        const processedReviews = message.reviews.map(review => {
                            const processedImages = review.images.map(url => {
                                const processed = processImageUrl(url);
                                console.log('原始URL:', url);
                                console.log('处理后URL:', processed);
                                return processed;
                            });
                            return { images: processedImages };
                        });

                        // 发送消息到目标标签页
                        chrome.tabs.sendMessage(targetTab.id, {
                            type: 'startProcessing',
                            reviews: processedReviews
                        }, function(response) {
                            if (chrome.runtime.lastError) {
                                console.error('发送消息失败:', chrome.runtime.lastError);
                                sendResponse({ success: false, error: chrome.runtime.lastError.message });
                            } else {
                                console.log('消息发送成功，响应:', response);
                                sendResponse({ success: true });
                            }
                        });
                    });
                } else {
                    // 创建新标签页
                    chrome.tabs.create({
                        url: 'https://ses.zzds888.com:1655/Task/AddTask',
                        active: true
                    }, function(tab) {
                        targetTab = tab;
                        console.log('创建新标签页:', targetTab.id);
                        
                        // 等待页面加载完成后发送消息
                        setTimeout(() => {
                            // 预处理所有图片URL
                            const processedReviews = message.reviews.map(review => {
                                const processedImages = review.images.map(url => {
                                    const processed = processImageUrl(url);
                                    console.log('原始URL:', url);
                                    console.log('处理后URL:', processed);
                                    return processed;
                                });
                                return { images: processedImages };
                            });

                            chrome.tabs.sendMessage(targetTab.id, {
                                type: 'startProcessing',
                                reviews: processedReviews
                            }, function(response) {
                                if (chrome.runtime.lastError) {
                                    console.error('发送消息失败:', chrome.runtime.lastError);
                                    sendResponse({ success: false, error: chrome.runtime.lastError.message });
                                } else {
                                    console.log('消息发送成功，响应:', response);
                                    sendResponse({ success: true });
                                }
                            });
                        }, 3000); // 等待3秒确保页面加载
                    });
                }
            });
            
            return true; // 保持消息通道开放
        }
    }
    // 处理下载图片请求
    if (message.type === 'downloadImage') {
        console.log('收到下载图片请求:');
        console.log('原始URL:', message.url);
        
        // 处理图片URL
        const processedUrl = processImageUrl(message.url);
        console.log('处理后的下载URL:', processedUrl);
        console.log('文件名:', message.filename || 'taobao_image.jpg');
        
        // 分析URL的构成
        const urlParts = processedUrl.split('.');
        const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1].toLowerCase() : 'jpg';
        console.log('检测到的文件扩展名:', extension);
        
        chrome.downloads.download({
            url: processedUrl,
            filename: message.filename || `taobao_image.${extension}`,
            saveAs: false // 直接下载，不弹出保存对话框
        }, (downloadId) => {
            if (chrome.runtime.lastError) {
                console.error('下载失败:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.log('下载成功，ID:', downloadId);
                sendResponse({ success: true, downloadId: downloadId });
            }
        });
        
        return true; // 保持消息通道开放，以便异步回复
    }
    
    // 新增的消息处理
    if (message.type === 'queryTabs') {
        chrome.tabs.query({ url: message.url }, (tabs) => {
            sendResponse(tabs);
        });
        return true; // 保持消息通道开放
    }
    
    if (message.type === 'sendToTab') {
        chrome.tabs.sendMessage(message.tabId, {
            type: 'fillData',
            data: message.data
        });
    }
    
    if (message.type === 'activateTab') {
        chrome.tabs.update(message.tabId, { active: true });
    }
    
    if (message.type === 'createTab') {
        chrome.tabs.create({ url: message.url }, (tab) => {
            // 监听标签页加载完成
            const listener = (tabId, changeInfo) => {
                if (tabId === tab.id && changeInfo.status === 'complete') {
                    // 等待一段时间确保页面完全加载
                    setTimeout(() => {
                        chrome.tabs.sendMessage(tab.id, {
                            type: 'fillData',
                            data: message.data
                        });
                    }, 1000);
                    
                    // 移除监听器
                    chrome.tabs.onUpdated.removeListener(listener);
                }
            };
            chrome.tabs.onUpdated.addListener(listener);
        });
    }
});

// 错误处理
chrome.runtime.onInstalled.addListener(() => {
    console.log('扩展已安装/更新');
});

// 标签页关闭处理
chrome.tabs.onRemoved.addListener((tabId) => {
    console.log('标签页关闭:', tabId);
});