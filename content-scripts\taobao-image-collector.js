// 图片收集器 (淘宝)
window.TaobaoImageCollector = class ImageCollector {
    constructor() {
        this.selectedGroups = new Map(); // 存储选中的图片组 { reviewIndex: [url1, url2] }
        this.controlPanelAdded = false;  // 标记控制面板是否已添加
        this.isProcessing = false; // 标记是否正在处理转移
        this.currentPopupContainer = null; // 记录当前处理的弹窗
    }

    // 初始化界面，添加复选框和控制面板
    initializeInterface() {
        Utils.log('淘宝收集器', '初始化界面');
        this.addCheckboxesAndPanelListener();
    }

    // 监听DOM变化，添加复选框和控制面板
    addCheckboxesAndPanelListener() {
        Utils.log('淘宝收集器', '开始监听DOM变化以添加复选框和控制面板');

        // 先尝试处理已有的评价弹窗
        this.checkExistingPopup();

        const observer = new MutationObserver((mutations) => {
            // 使用防抖处理，避免频繁触发
            clearTimeout(this._debounceTimer);
            this._debounceTimer = setTimeout(() => {
                // 查找淘宝评价弹窗容器
                this.checkExistingPopup();
            }, AppConfig.DELAYS.DOM_CHECK_DEBOUNCE || 300); // 使用配置的防抖时间
        });

        // 监听DOM变化
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true // 捕获弹窗显示/隐藏
        });

        // 添加特定事件监听 (如点击评论按钮、标签切换)
        document.addEventListener('click', (e) => {
            // 检测评论相关按钮点击
            const reviewButton = e.target.closest(AppConfig.SELECTORS.TAOBAO.REVIEW_BUTTON_TRIGGERS.join(', '));
            if (reviewButton) {
                Utils.log('淘宝收集器', '检测到评论按钮点击，等待弹窗出现');
                setTimeout(() => this.checkExistingPopup(), AppConfig.DELAYS.POPUP_LOAD_DELAY || 500);
            }

            // 标签切换点击
            const tabElement = e.target.closest(AppConfig.SELECTORS.TAOBAO.TAB_ITEMS.join(', '));
            if (tabElement && this.currentPopupContainer) {
                Utils.log('淘宝收集器', '检测到标签切换点击');
                setTimeout(() => {
                    // 重置当前弹窗的状态
                    this.resetControlPanel(this.currentPopupContainer);
                    this.selectedGroups.clear();
                    this.processNewReviews(this.currentPopupContainer); // 处理新加载的评论
                    this.updateSelectedCount();
                }, AppConfig.DELAYS.TAB_SWITCH || 800);
            }
        });
    }

    // 检查是否存在评价弹窗，如果存在则处理
    checkExistingPopup() {
        const popupContainer = Utils.findElement(AppConfig.SELECTORS.TAOBAO.POPUP_CONTAINER_VARIANTS);
        if (popupContainer && window.getComputedStyle(popupContainer).display !== 'none') {
            // 检查是否是新的弹窗或之前未处理的弹窗
            if (popupContainer !== this.currentPopupContainer || !this.controlPanelAdded) {
                Utils.log('淘宝收集器', '发现新的或未处理的评价弹窗');
                this.currentPopupContainer = popupContainer;
                this.controlPanelAdded = false; // 重置添加标记
                this.selectedGroups.clear(); // 清空之前的选择
                this.addControlPanelToPopup(popupContainer);
                this.processNewReviews(popupContainer);
                this.updateSelectedCount();
            } else {
                // 弹窗已存在且已处理，可能需要检查更新
                this.processNewReviews(popupContainer);
            }
        } else {
            // 如果之前记录的弹窗消失了，重置状态
            if (this.currentPopupContainer) {
                 Utils.log('淘宝收集器', '评价弹窗已关闭或消失，重置状态');
                 this.currentPopupContainer = null;
                 this.controlPanelAdded = false;
                 this.selectedGroups.clear();
            }
        }
    }

    // 重置控制面板状态（例如移除旧面板）
    resetControlPanel(container) {
         const existingPanel = container.querySelector(`#${AppConfig.CLASSNAMES.CONTROL_PANEL_TAOBAO}`);
         if (existingPanel) {
             existingPanel.remove();
             Utils.log('淘宝收集器', '已移除旧的控制面板');
         }
         this.controlPanelAdded = false;
    }

    // 添加控制面板到评价弹窗
    addControlPanelToPopup(popupContainer) {
        if (this.controlPanelAdded) return; // 已添加则跳过

        // 查找适合放置控制面板的位置
        const targetElement = this.findPanelTargetElement(popupContainer);
        if (!targetElement) {
            Utils.log('淘宝收集器', '未找到合适的控制面板放置位置', 'warn');
            return;
        }

        // 创建控制面板
        const panel = this.createControlPanelElement();
        
        // 插入控制面板
        // 尝试插入到目标元素的后面，或作为其子元素
        if (targetElement.parentElement) {
             if(targetElement.nextSibling){
                  targetElement.parentElement.insertBefore(panel, targetElement.nextSibling);
             } else {
                  targetElement.parentElement.appendChild(panel);
             }
            Utils.log('淘宝收集器', '控制面板已添加到页面');
            this.controlPanelAdded = true;
            this.initializePanelEventListeners(panel);
        } else {
            Utils.log('淘宝收集器', '目标元素无父节点，无法插入控制面板', 'error');
        }
    }

    // 查找控制面板的目标插入位置
    findPanelTargetElement(popupContainer) {
         let target = null;
         // 尝试策略1：查找特定的标题/工具栏区域
         target = Utils.findElement(AppConfig.SELECTORS.TAOBAO.PANEL_TARGET_PRIMARY, popupContainer);
         if (target) return target;
         
         // 尝试策略2：查找评论过滤/排序区域
         target = Utils.findElement(AppConfig.SELECTORS.TAOBAO.PANEL_TARGET_SECONDARY, popupContainer);
         if (target) return target;

         // 尝试策略3：查找第一个评论项，插入到它前面
         const firstComment = Utils.findElement(AppConfig.SELECTORS.TAOBAO.REVIEW_ITEM, popupContainer);
         if (firstComment) return firstComment; // 返回第一个评论，会在其前面插入
         
         // 兜底：返回弹窗容器本身，将插入到顶部
         return popupContainer;
    }

    // 创建控制面板的DOM元素
    createControlPanelElement() {
        const panel = Utils.createElement('div', {
            id: AppConfig.CLASSNAMES.CONTROL_PANEL_TAOBAO,
            style: AppConfig.STYLES.CONTROL_PANEL
        });

        panel.innerHTML = `
            <label style="${AppConfig.STYLES.LABEL_STYLE}">
                <input type="checkbox" id="selectAllTaobaoReviews" style="${AppConfig.STYLES.CHECKBOX_STYLE}"> 全选
            </label>
            <button id="autoTransferTaobao" style="${AppConfig.STYLES.BUTTON_PRIMARY} margin-left: 8px;">
                一键转移(<span id="selectedTaobaoCount">0</span>)
            </button>
            <button id="stopTransferTaobao" style="${AppConfig.STYLES.BUTTON_DANGER} margin-left: 5px; display: none;">
                终止转移
            </button>
        `;
        return panel;
    }

    // 初始化控制面板的事件监听
    initializePanelEventListeners(panel) {
        const selectAllCheckbox = panel.querySelector('#selectAllTaobaoReviews');
        const transferButton = panel.querySelector('#autoTransferTaobao');
        const stopButton = panel.querySelector('#stopTransferTaobao');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                 Utils.log('淘宝收集器', `全选状态改变: ${e.target.checked}`);
                 const checkboxes = this.currentPopupContainer?.querySelectorAll(`.${AppConfig.CLASSNAMES.CHECKBOX_TAOBAO}`) || [];
                 checkboxes.forEach(checkbox => {
                     if (checkbox.checked !== e.target.checked) {
                         checkbox.checked = e.target.checked;
                         this.handleCheckboxChange(checkbox); // 触发单选框的处理逻辑
                     }
                 });
            });
        }

        if (transferButton) {
            transferButton.addEventListener('click', () => {
                Utils.log('淘宝收集器', '点击一键转移');
                this.handleTransfer();
            });
        }

        if (stopButton) {
            stopButton.addEventListener('click', () => {
                 Utils.log('淘宝收集器', '点击终止转移');
                 // TODO: 实现终止逻辑，可能需要通知 background
                 // chrome.runtime.sendMessage({ type: 'stopProcessing' });
                 this.updateButtonState(false); // 暂时只更新按钮状态
            });
        }
    }

    // 处理新的评价列表
    processNewReviews(container) {
        if (!container) return;
        Utils.log('淘宝收集器', '处理新的评价列表');
        const reviewItems = Utils.findAllElements(AppConfig.SELECTORS.TAOBAO.REVIEW_ITEM, container);
        Utils.log('淘宝收集器', `找到 ${reviewItems.length} 个评价项`);

        if (reviewItems.length === 0) {
            // 延迟重试，可能内容未完全加载
            setTimeout(() => {
                 const retryItems = Utils.findAllElements(AppConfig.SELECTORS.TAOBAO.REVIEW_ITEM, container);
                 if (retryItems.length > 0) {
                     Utils.log('淘宝收集器', `延迟后找到 ${retryItems.length} 个评价项`);
                     this.processReviewItems(retryItems);
                 }
            }, AppConfig.DELAYS.RETRY_REVIEW || 1000);
            return;
        }

        this.processReviewItems(reviewItems);
    }

    // 处理单个评价项集合
    processReviewItems(reviewItems) {
        reviewItems.forEach((review, index) => {
            // 检查是否已添加复选框
            if (review.querySelector(`.${AppConfig.CLASSNAMES.CHECKBOX_WRAPPER_TAOBAO}`)) {
                return; // 已处理
            }

            // 查找图片区域
            const albumArea = Utils.findElement(AppConfig.SELECTORS.TAOBAO.ALBUM_AREA, review);
            if (albumArea) {
                // 尝试添加复选框（会检查图片是否存在）
                this.addCheckboxToAlbum(albumArea, review, index);
            } else {
                 Utils.log('淘宝收集器', `评价项 ${index} 未找到图片区域`, 'warn');
                 // 可能需要延迟重试查找 albumArea
                 setTimeout(() => {
                      const retryAlbumArea = Utils.findElement(AppConfig.SELECTORS.TAOBAO.ALBUM_AREA, review);
                      if (retryAlbumArea && !review.querySelector(`.${AppConfig.CLASSNAMES.CHECKBOX_WRAPPER_TAOBAO}`)) {
                          this.addCheckboxToAlbum(retryAlbumArea, review, index);
                      }
                 }, AppConfig.DELAYS.RETRY_ALBUM || 500);
            }
        });
    }

    // 给相册区域添加复选框
    addCheckboxToAlbum(albumArea, review, index) {
        // 检查是否有图片
        const images = albumArea.querySelectorAll('img');
        if (images.length === 0) {
            Utils.log('淘宝收集器', '相册区域无图片，可能正在加载或无图评价');
            // 可以选择监听图片加载或直接放弃
            // 监听示例:
            const observer = new MutationObserver((mutations, obs) => {
                 const updatedImages = albumArea.querySelectorAll('img');
                 if (updatedImages.length > 0) {
                     Utils.log('淘宝收集器', '图片已加载，添加复选框');
                     this.createCheckboxWrapper(albumArea, review, index);
                     obs.disconnect();
                 }
            });
            observer.observe(albumArea, { childList: true, subtree: true });
            // 超时取消监听
            setTimeout(() => observer.disconnect(), AppConfig.DELAYS.IMAGE_LOAD_TIMEOUT || 5000);
            return; // 暂时不添加复选框
        }

        // 有图片，创建并添加复选框包装器
        this.createCheckboxWrapper(albumArea, review, index);
    }

    // 创建复选框包装器及内部元素
    createCheckboxWrapper(albumArea, review, index) {
        if (albumArea.querySelector(`.${AppConfig.CLASSNAMES.CHECKBOX_WRAPPER_TAOBAO}`)) {
            return; // 防止重复添加
        }

        const wrapper = Utils.createElement('div', {
            className: AppConfig.CLASSNAMES.CHECKBOX_WRAPPER_TAOBAO,
            style: AppConfig.STYLES.CHECKBOX_WRAPPER
        });

        // 复选框
        const checkbox = Utils.createElement('input', {
            attrs: { type: 'checkbox', 'data-review-index': index },
            className: AppConfig.CLASSNAMES.CHECKBOX_TAOBAO,
            style: AppConfig.STYLES.CHECKBOX
        });
        checkbox.addEventListener('change', () => this.handleCheckboxChange(checkbox));
        wrapper.appendChild(checkbox);

        // 复制按钮
        const copyButton = Utils.createElement('button', {
            text: '复制',
            style: AppConfig.STYLES.BUTTON_ACTION,
            attrs: { 'data-review-index': index, title: '复制这组图片到剪贴板' }
        });
        copyButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleCopyImages(albumArea);
        });
        wrapper.appendChild(copyButton);

        // 下载按钮
        const downloadButton = Utils.createElement('button', {
            text: '下载',
            style: `${AppConfig.STYLES.BUTTON_ACTION} margin-left: 5px; background-color: #009688;`,
            attrs: { 'data-review-index': index, title: '下载这组图片' }
        });
        downloadButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleDownloadImages(albumArea);
        });
        wrapper.appendChild(downloadButton);

        // 强制设置父容器相对定位以正确定位绝对位置的 wrapper
        albumArea.style.position = 'relative';
        albumArea.appendChild(wrapper);
        Utils.log('淘宝收集器', `复选框包装器已添加到评价 ${index}`);
    }

    // 处理复选框状态变化
    handleCheckboxChange(checkbox) {
        const review = checkbox.closest(AppConfig.SELECTORS.TAOBAO.REVIEW_ITEM);
        if (!review) return;
        const albumArea = review.querySelector(AppConfig.SELECTORS.TAOBAO.ALBUM_AREA);
        if (!albumArea) return;

        const reviewIndex = checkbox.dataset.reviewIndex;

        if (checkbox.checked) {
            // 确保评价可见（可选）
            // review.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // 延迟获取图片，确保图片已加载
            setTimeout(() => {
                const images = albumArea.querySelectorAll('img');
                const imageUrls = Array.from(images).map(img => img.src).filter(src => src);
                if (imageUrls.length > 0) {
                    this.selectedGroups.set(reviewIndex, imageUrls);
                    Utils.log('淘宝收集器', `选中组 ${reviewIndex}，包含 ${imageUrls.length} 张图片`);
                } else {
                     Utils.log('淘宝收集器', `选中组 ${reviewIndex} 时未找到有效图片`, 'warn');
                     checkbox.checked = false; // 取消选择无效的组
                }
                this.updateSelectedCount();
            }, AppConfig.DELAYS.IMAGE_LOAD || 500);
        } else {
            this.selectedGroups.delete(reviewIndex);
            Utils.log('淘宝收集器', `取消选中组 ${reviewIndex}`);
            this.updateSelectedCount();
        }
    }

    // 更新选中数量显示
    updateSelectedCount() {
        const count = this.selectedGroups.size;
        const countElement = document.getElementById('selectedTaobaoCount');
        if (countElement) {
            countElement.textContent = count;
        }
        // 更新全选框状态
        const selectAllCheckbox = document.getElementById('selectAllTaobaoReviews');
        if (selectAllCheckbox && this.currentPopupContainer) {
             const allCheckboxes = this.currentPopupContainer.querySelectorAll(`.${AppConfig.CLASSNAMES.CHECKBOX_TAOBAO}`);
             // 只有当所有可见的复选框都被选中时，全选框才被选中
             selectAllCheckbox.checked = allCheckboxes.length > 0 && count === allCheckboxes.length;
             // 处理不确定状态 (可选)
             selectAllCheckbox.indeterminate = count > 0 && count < allCheckboxes.length;
        }
    }

    // 处理图片转移
    async handleTransfer() {
        if (this.selectedGroups.size === 0) {
            alert('请至少选择一组图片进行转移。');
            return;
        }

        Utils.log('淘宝收集器', `开始转移 ${this.selectedGroups.size} 组图片`);
        this.updateButtonState(true); // 设置为处理中状态

        try {
            // 确保所有选中的评价都加载完成（如果需要滚动加载）
            // await this.ensureReviewsLoaded(); // 这个逻辑可能比较复杂，暂时简化

            // 重新收集一次，确保数据最新
            const currentSelectedGroups = new Map();
            const checkedCheckboxes = this.currentPopupContainer?.querySelectorAll(`.${AppConfig.CLASSNAMES.CHECKBOX_TAOBAO}:checked`) || [];
            
            for(const cb of checkedCheckboxes) {
                const idx = cb.dataset.reviewIndex;
                const reviewEl = cb.closest(AppConfig.SELECTORS.TAOBAO.REVIEW_ITEM);
                const albumEl = reviewEl?.querySelector(AppConfig.SELECTORS.TAOBAO.ALBUM_AREA);
                const imgs = albumEl?.querySelectorAll('img');
                const urls = Array.from(imgs || []).map(img => img.src).filter(src => src);
                if(urls.length > 0) {
                    currentSelectedGroups.set(idx, urls);
                }
            }
            
            if (currentSelectedGroups.size === 0) {
                 throw new Error('重新收集选中的图片时发现没有有效的图片组');
            }

            // 转换数据格式并排序
            const reviewsToSend = Array.from(currentSelectedGroups.entries())
                .sort((a, b) => Number(a[0]) - Number(b[0])) // 按 reviewIndex 排序
                .map(([_, images]) => ({ images })); // 转换格式

            Utils.log('淘宝收集器', '准备发送数据到 Background', reviewsToSend);

            // 发送消息到 background script
            const response = await chrome.runtime.sendMessage({
                type: 'processReviews', // 保持与 background 一致的消息类型
                reviews: reviewsToSend
            });

            if (response?.success) {
                Utils.log('淘宝收集器', '转移请求已成功发送');
                alert(`成功将 ${reviewsToSend.length} 组图片发送到处理标签页！`);
            } else {
                const errorMsg = response?.error || '未知错误';
                Utils.log('淘宝收集器', `发送转移请求失败: ${errorMsg}`, 'error');
                throw new Error(`发送转移请求失败: ${errorMsg}`);
            }
        } catch (error) {
            console.error('处理转移失败:', error);
            Utils.log('淘宝收集器', `处理转移失败: ${error.message}`, 'error');
            alert(`转移处理失败: ${error.message}`);
        } finally {
            this.updateButtonState(false); // 恢复按钮状态
        }
    }

    // 更新按钮状态（处理中/默认）
    updateButtonState(isProcessing) {
        const transferButton = document.getElementById('autoTransferTaobao');
        const stopButton = document.getElementById('stopTransferTaobao');
        const selectAllCheckbox = document.getElementById('selectAllTaobaoReviews');

        if (transferButton && stopButton) {
            transferButton.style.display = isProcessing ? 'none' : 'inline-block';
            stopButton.style.display = isProcessing ? 'inline-block' : 'none';
        }
        if (selectAllCheckbox) {
             selectAllCheckbox.disabled = isProcessing;
        }
        // 禁用所有单选框
        const checkboxes = this.currentPopupContainer?.querySelectorAll(`.${AppConfig.CLASSNAMES.CHECKBOX_TAOBAO}`) || [];
        checkboxes.forEach(cb => cb.disabled = isProcessing);
        
        this.isProcessing = isProcessing;
    }

    // 处理复制图片到剪贴板
    async handleCopyImages(albumArea) {
        Utils.log('淘宝收集器', '处理复制图片');
        const images = albumArea.querySelectorAll('img');
        if (!images || images.length === 0) {
            alert('未找到图片，无法复制');
            return;
        }

        const statusIndicator = this.showStatusIndicator(albumArea, '复制中...');

        try {
            if (images.length === 1) {
                // 单张图片尝试直接复制图像
                const originalUrl = this.getOriginalImageUrl(images[0].src);
                try {
                    const response = await fetch(originalUrl, { mode: 'cors', cache: 'no-cache' });
                    if (!response.ok) throw new Error(`获取图片失败: ${response.status}`);
                    const blob = await response.blob();
                    await navigator.clipboard.write([new ClipboardItem({ [blob.type]: blob })]);
                    this.updateStatusIndicator(statusIndicator, '复制成功', 'success');
                } catch (error) {
                    Utils.log('淘宝收集器', `直接复制图片失败(${error.message})，回退复制URL`, 'warn');
                    this.copyTextToClipboard(originalUrl);
                    this.updateStatusIndicator(statusIndicator, '已复制链接', 'warning');
                }
            } else {
                // 多张图片复制链接列表
                const urls = Array.from(images).map(img => this.getOriginalImageUrl(img.src));
                this.copyTextToClipboard(urls.join('\n'));
                this.updateStatusIndicator(statusIndicator, `复制 ${urls.length} 张链接`, 'success');
            }
        } catch (error) {
            console.error('复制图片出错:', error);
            this.updateStatusIndicator(statusIndicator, '复制失败', 'error');
            alert('复制图片失败: ' + error.message);
        }
    }

    // 处理下载图片
    handleDownloadImages(albumArea) {
        Utils.log('淘宝收集器', '处理下载图片');
        const images = albumArea.querySelectorAll('img');
        if (!images || images.length === 0) {
            alert('未找到图片，无法下载');
            return;
        }

        const statusIndicator = this.showStatusIndicator(albumArea, '准备下载...');
        const itemId = this.getItemIdFromUrl() || 'taobao';
        const timestamp = Date.now();
        let downloadCount = 0;
        let errorCount = 0;

        Array.from(images).forEach((img, imgIndex) => {
            const originalUrl = this.getOriginalImageUrl(img.src);
            const filename = `${itemId}_${timestamp}_${imgIndex + 1}.jpg`;
            
            chrome.runtime.sendMessage({ type: 'downloadImage', url: originalUrl, filename }, (response) => {
                if (response?.success) {
                    downloadCount++;
                } else {
                    errorCount++;
                    Utils.log('淘宝收集器', `下载失败: ${filename} - ${response?.error || '未知错误'}`, 'error');
                }
                // 更新状态
                if (downloadCount + errorCount === images.length) {
                    const finalMsg = errorCount === 0 ? `下载完成 (${downloadCount})` : `下载 ${downloadCount}, 失败 ${errorCount}`;
                    const finalType = errorCount === 0 ? 'success' : 'warning';
                    this.updateStatusIndicator(statusIndicator, finalMsg, finalType);
                } else {
                     this.updateStatusIndicator(statusIndicator, `下载中 ${downloadCount + errorCount}/${images.length}...`);
                }
            });
        });
    }

    // 从当前页面URL获取商品ID
    getItemIdFromUrl() {
        const match = window.location.href.match(/item\.htm\?.*?id=(\d+)/);
        return match ? match[1] : null;
    }

    // 获取原始大图URL (复用逻辑)
    getOriginalImageUrl(url) {
        // 这里的实现应该与 background.js 中的 processImageUrl 保持一致或调用通用函数
        // 暂时简化，直接返回处理过的 URL，假设 Utils 中有此方法
        return Utils.processImageUrl ? Utils.processImageUrl(url) : url.replace(/_\d+x\d+\.jpg.*/, '');
    }

    // 显示状态指示器
    showStatusIndicator(albumArea, message) {
        // 移除旧的指示器
        albumArea.querySelectorAll('.status-indicator-taobao').forEach(el => el.remove());
        
        const indicator = Utils.createElement('div', {
            className: 'status-indicator-taobao',
            text: message,
            style: AppConfig.STYLES.STATUS_INDICATOR_INLINE
        });
        albumArea.appendChild(indicator);
        
        // 设置定时器自动移除
        setTimeout(() => {
             if (indicator && indicator.parentNode) {
                 indicator.remove();
             }
        }, 4000);
        
        return indicator;
    }

    // 更新状态指示器
    updateStatusIndicator(indicator, message, type = 'info') {
        if (!indicator || !indicator.parentNode) return;
        indicator.textContent = message;
        indicator.className = `status-indicator-taobao ${type}`;
    }

    // 复制文本到剪贴板 (复用Utils)
    copyTextToClipboard(text) {
        if (Utils.copyTextToClipboard) {
             Utils.copyTextToClipboard(text);
        } else {
            // 简易回退
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            Utils.log('复制', '文本已复制 (使用回退方法)');
        }
    }
} 