/**
 * 粘贴管理模块
 * 负责处理粘贴区域和粘贴功能
 */

/**
 * 粘贴区域管理器
 * 负责在页面上创建和管理用于粘贴图片URL的区域
 */

// 粘贴区域管理器类 - 整合了 site-a-content.js 中的逻辑
window.PasteAreaManager = class PasteAreaManager {
    constructor(imageUploaderInstance) {
        this.pasteAreas = new Map(); // 存储每个上传按钮对应的粘贴区域 { uploadButtonElement: pasteAreaElement }
        this.imageUploader = imageUploaderInstance; // 依赖注入 ImageUploader 实例
        this.maxRetries = 3;
        this.retryDelay = 500;
    }

    /**
     * 初始化，查找上传按钮并为其添加粘贴区域
     */
    initialize() {
        Utils.log('粘贴管理', '初始化粘贴区域管理器');
        
        // 检查是否为网站A
        if (!window.location.href.includes(AppConfig.SITES.SITE_A.URL_PATTERN)) {
            Utils.log('粘贴管理', '非网站A，不初始化粘贴区域');
            return;
        }
        
        // 监听DOM变化，以便为动态添加的上传按钮创建粘贴区域
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    // 检查添加的节点本身或其子节点是否为任务容器
                    if (node.nodeType === 1) { // Element node
                        if (node.matches && node.matches(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER)) {
                             this.processNewTaskContainer(node);
                        } else {
                            // 检查子节点
                             const containers = node.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
                             containers.forEach(container => this.processNewTaskContainer(container));
                        }
                    }
                });
            });
        });

        // 开始观察整个文档的子节点变化
        observer.observe(document.body, { childList: true, subtree: true });

        // 初始化时处理页面上已存在的任务容器
        const existingContainers = document.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
        Utils.log('粘贴管理', `初始化时发现 ${existingContainers.length} 个任务容器`);
        existingContainers.forEach(container => this.processNewTaskContainer(container));
        
        Utils.log('粘贴管理', '初始化完成，开始监听新任务容器');
    }

    /**
     * 处理新的任务容器：查找上传按钮并添加粘贴区域
     * @param {Element} container - 任务容器元素
     */
    processNewTaskContainer(container) {
        // 查找容器内的上传按钮
        const uploadButton = container.querySelector(AppConfig.SELECTORS.SITE_A.UPLOAD_BUTTON);
        if (uploadButton && !this.pasteAreas.has(uploadButton)) {
             Utils.log('粘贴管理', '为新的上传按钮添加粘贴区域');
             this.addPasteArea(uploadButton, container);
        } else if (uploadButton && this.pasteAreas.has(uploadButton)) {
             // Utils.log('粘贴管理', '上传按钮已有关联的粘贴区域');
        } else {
             Utils.log('粘贴管理', '在任务容器中未找到上传按钮', 'warn');
        }
    }

    /**
     * 为指定的上传按钮添加粘贴区域
     * @param {Element} uploadButton - 上传按钮元素
     * @param {Element} taskContainer - 所属的任务容器
     */
    addPasteArea(uploadButton, taskContainer) {
        // 检查是否已存在粘贴区，避免重复添加
        if (taskContainer.querySelector('.paste-image-area')) {
             Utils.log('粘贴管理', '粘贴区域已存在，跳过添加');
            return;
        }
        
        Utils.log('粘贴管理', '创建粘贴区域');
        const pasteArea = Utils.createElement('div', {
            class: 'paste-image-area',
            style: AppConfig.STYLES.PASTE_AREA,
            content: '<span>或 <b>粘贴</b> 图片URL (Ctrl+V)</span>',
            tabindex: '0' // 使 div 可聚焦以接收粘贴事件
        });

        // 将粘贴区域插入到上传按钮附近 (例如，按钮的父级或特定容器内)
        const parentContainer = uploadButton.closest(AppConfig.SELECTORS.SITE_A.UPLOAD_BUTTON_CONTAINER) || uploadButton.parentElement;
        if (parentContainer) {
            parentContainer.appendChild(pasteArea);
             Utils.log('粘贴管理', '粘贴区域已添加到DOM');
        } else {
             Utils.log('粘贴管理', '找不到合适的父容器来放置粘贴区域', 'error');
             return; // 无法添加则返回
        }
        
        // 记录关联关系
        this.pasteAreas.set(uploadButton, pasteArea);

        // 添加粘贴事件监听
        pasteArea.addEventListener('paste', async (event) => {
            Utils.log('粘贴事件', '检测到粘贴操作');
            event.preventDefault();
            event.stopPropagation();

            // 获取粘贴的文本
            const pastedText = (event.clipboardData || window.clipboardData).getData('text');
            if (!pastedText) {
                Utils.log('粘贴事件', '粘贴内容为空', 'warn');
                return;
            }
            
            // 简单的URL验证 (可以根据需要加强)
            if (!pastedText.startsWith('http')) {
                 Utils.log('粘贴事件', '粘贴内容不是有效的URL: ' + pastedText, 'warn');
                 pasteArea.innerHTML = '<span style="color: red;">无效URL!</span>';
                 setTimeout(() => { pasteArea.innerHTML = '<span>或 <b>粘贴</b> 图片URL (Ctrl+V)</span>'; }, 2000);
                 return;
            }
            
            const imageUrl = pastedText.trim();
            Utils.log('粘贴事件', '粘贴的图片URL: ' + imageUrl);
            
            // 更新粘贴区域状态为处理中
            pasteArea.innerHTML = '<span style="color: orange;">处理中...</span>';
            pasteArea.style.borderColor = 'orange';

            try {
                // 获取上传区域
                const uploadArea = uploadButton.closest(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
                if (!uploadArea) {
                     throw new Error('无法找到关联的任务容器');
                }
                
                // 调用 ImageUploader 处理单张图片
                // 注意：这里假设 ImageUploader 实例可用
                // TODO: 需要找到或传入 ImageUploader 实例
                if (window.imageUploaderInstance && typeof window.imageUploaderInstance.processSingleImage === 'function') {
                    // 模拟 processSingleImage 需要的参数
                    const result = await window.imageUploaderInstance.processSingleImage(
                        imageUrl, 
                        uploadArea, 
                        -1, // 表示非批量处理中的组号
                        1   // 表示单张图片
                    );
                    
                    if (result.success) {
                        Utils.log('粘贴事件', '图片处理成功');
                        pasteArea.innerHTML = '<span style="color: green;">上传成功!</span>';
                        pasteArea.style.borderColor = 'green';
                    } else {
                         Utils.log('粘贴事件', '图片处理失败: ' + result.message, 'error');
                         pasteArea.innerHTML = '<span style="color: red;">上传失败!</span>';
                         pasteArea.style.borderColor = 'red';
                    }
                } else {
                    Utils.log('粘贴管理', 'ImageUploader 实例不可用或方法不存在', 'error');
                    throw new Error('图片上传服务不可用');
                }

            } catch (error) {
                console.error('处理粘贴的图片URL时出错:', error);
                Utils.log('粘贴事件', '处理URL失败: ' + error.message, 'error');
                pasteArea.innerHTML = '<span style="color: red;">处理失败!</span>';
                pasteArea.style.borderColor = 'red';
            } finally {
                 // 一段时间后恢复默认状态
                 setTimeout(() => {
                     pasteArea.innerHTML = '<span>或 <b>粘贴</b> 图片URL (Ctrl+V)</span>';
                     pasteArea.style.borderColor = '#ccc'; // 恢复边框颜色
                 }, 3000);
            }
        });
        
        // 添加点击事件，方便用户知道可以粘贴
        pasteArea.addEventListener('click', () => {
             pasteArea.focus(); // 聚焦以接收粘贴
             // 可以显示一个提示
             const originalText = pasteArea.innerHTML;
             pasteArea.innerHTML = '<span style="color: #1E9FFF;">请按 Ctrl+V 粘贴</span>';
             setTimeout(() => { pasteArea.innerHTML = originalText; }, 1500);
        });
    }
};
