/**
 * AI功能模块
 * 负责AI分析标题、生成关键词和相关UI交互
 */

// AI功能管理器
window.AIFeatureManager = class AIFeatureManager {
    constructor() {
        // 基础设置
        this.initialized = false;
        this.taskCount = 0; // 默认任务数量
        this.debugMode = window.AppConfig?.DEBUG ?? false;
        this.aiSettings = Utils.getAISettings(); // 从Utils获取设置
    }
    
    /**
     * 初始化AI功能
     */
    initialize() {
        Utils.log('AI功能', '初始化AI功能管理器');
        
        if (this.initialized) {
            Utils.log('AI功能', 'AI功能管理器已初始化，不重复执行');
            return;
        }
        
        try {
            // 检查是否为目标页面 (网站A)
            const currentUrl = window.location.href;
            const isSiteA = currentUrl.includes(AppConfig.SITES.SITE_A.URL_PATTERN);
            
            if (!isSiteA) {
                Utils.log('AI功能', '当前页面不是网站A发布页面，不初始化AI功能');
                return;
            }
            
            Utils.log('AI功能', '检测到网站A发布页面');
            
            // 检测是否为重新发布页面
            const isRepost = currentUrl.includes('TaskId=');
            if (isRepost) {
                Utils.log('AI功能', '检测到重新发布页面，启用特殊处理');
                // 确保在正确的上下文中执行 (可能在iframe中)
                setTimeout(() => this.ensureContentLoaded(), 1000);
            } else {
                 // 对于非重新发布页面，直接更新任务数量
                 this.updateTaskCount(document); // 传入主文档上下文
            }
            
            this.initialized = true;
            Utils.log('AI功能', 'AI功能管理器初始化完成');
        } catch (error) {
            console.error('AI功能管理器初始化失败:', error);
        }
    }
    
    /**
     * 确保重新发布页面内容加载完成 (处理iframe)
     */
    ensureContentLoaded() {
        Utils.log('AI功能', '检查iframe内容是否已加载');
        
        const iframes = document.querySelectorAll('iframe');
        if (iframes && iframes.length > 0) {
            Utils.log('AI功能', `发现${iframes.length}个iframe元素`);
            
            for (const iframe of iframes) {
                try {
                    // 检查iframe的src属性是否匹配目标页面
                    if (iframe.src && iframe.src.includes(AppConfig.SITES.SITE_A.IFRAME_PATTERN)) {
                        Utils.log('AI功能', `找到匹配的iframe: ${iframe.src}`);
                        
                        // 检查iframe是否已加载
                        if (iframe.contentDocument) {
                            this.checkIframeContent(iframe);
                        } else {
                            iframe.addEventListener('load', () => this.checkIframeContent(iframe));
                        }
                    }
                } catch (error) {
                    // 跨域错误是正常的，忽略
                    if (!error.message.includes('Blocked a frame with origin')) {
                        Utils.log('AI功能', `检查iframe失败: ${error.message}`, 'warn');
                    }
                }
            }
        } else {
            Utils.log('AI功能', '未发现iframe元素');
            // 如果没有iframe，假设在主文档，尝试更新任务数量
            this.updateTaskCount(document);
        }
    }
    
    /**
     * 检查iframe内容并进行必要的初始化
     */
    checkIframeContent(iframe) {
        try {
            const doc = iframe.contentDocument || iframe.contentWindow.document;
            Utils.log('AI功能', '成功访问iframe内容');
            
            // 检查关键元素是否存在以确认内容加载
            const titleInput = doc.querySelector(AppConfig.SELECTORS.SITE_A.PRODUCT_NAME_INPUT);
            if (titleInput) {
                Utils.log('AI功能', '在iframe中找到关键元素，更新任务数量');
                this.updateTaskCount(doc); // 在iframe文档上下文中更新
            } else {
                Utils.log('AI功能', '在iframe中未找到关键元素，可能仍在加载', 'warn');
                // 可以添加重试逻辑
                 setTimeout(() => this.checkIframeContent(iframe), 1500);
            }
        } catch (error) {
            Utils.log('AI功能', `检查iframe内容失败: ${error.message}`, 'error');
        }
    }
    
    /**
     * 更新任务数量
     * @param {Document} documentContext - 文档上下文 (主文档或iframe文档)
     */
    updateTaskCount(documentContext) {
        Utils.log('AI功能', '开始更新任务数量');
        try {
            let count = 1; // 默认值
            
            // 1. 通过任务容器选择器
            const taskContainers = documentContext.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
            if (taskContainers && taskContainers.length > 0) {
                count = taskContainers.length;
                Utils.log('AI功能', `通过任务容器选择器检测到任务数量: ${count}`);
                this.taskCount = count;
                return;
            }
            
            // 2. 通过关键词标签选择器
            const searchLabels = Array.from(documentContext.querySelectorAll('label')).filter(label =>
                label.textContent.trim().includes('搜索关键词') ||
                label.textContent.trim().includes('搜索词')
            );
            if (searchLabels && searchLabels.length > 0) {
                count = searchLabels.length;
                Utils.log('AI功能', `通过关键词标签检测到任务数量: ${count}`);
                this.taskCount = count;
                return;
            }
            
            // 3. 通过页面标题文本 (如 "第X单")
            const textElements = documentContext.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_COUNT_TEXT_ELEMENTS);
            for (const element of textElements) {
                const match = element.textContent.match(/第(\d+)单/);
                if (match && match[1]) {
                    const num = parseInt(match[1], 10);
                    if (!isNaN(num) && num > 0) {
                        count = num;
                        Utils.log('AI功能', `通过页面文本找到任务数量: ${count}`);
                        this.taskCount = count;
                        return;
                    }
                }
            }
            
            // 如果所有方法都失败，使用默认值
            this.taskCount = AppConfig.AI.DEFAULT_TASK_COUNT;
            Utils.log('AI功能', `未能自动检测到任务数量，使用默认值: ${this.taskCount}`);
            
        } catch (error) {
            console.error('更新任务数量时出错:', error);
            this.taskCount = AppConfig.AI.DEFAULT_TASK_COUNT; // 出错时使用默认值
            Utils.log('AI功能', `更新任务数量出错，使用默认值: ${this.taskCount}`, 'error');
        }
    }

    /**
     * 获取当前任务数量
     * @returns {number}
     */
    getTaskCount() {
        // 确保任务数量已更新
        if(this.taskCount === 0) {
             this.updateTaskCount(document); // 尝试在主文档更新
             if(this.taskCount === 0) {
                 // 如果主文档没有，尝试iframe
                 this.ensureContentLoaded();
             }
        }
        // 如果仍然是0，返回默认值
        return this.taskCount > 0 ? this.taskCount : AppConfig.AI.DEFAULT_TASK_COUNT;
    }
};

/**
 * 直接分析标题生成关键词（全局函数，方便调用）
 */
window.directAnalyzeTitle = async function() {
    Utils.log('直接分析', '开始直接分析标题');
    
    try {
        // 获取AI管理器实例（假设它已被初始化）
        // 注意：这里需要确保AIFeatureManager实例是可访问的
        // 更好的方式是将其作为参数传入，或通过某种方式获取单例
        
        // 1. 确定操作的文档上下文 (主文档或iframe)
        let documentContext = document;
        const iframes = document.querySelectorAll('iframe');
        for (const iframe of iframes) {
            try {
                 if (iframe.src && iframe.src.includes(AppConfig.SITES.SITE_A.IFRAME_PATTERN)) {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    // 简单检查iframe内是否有标题输入框
                    if (iframeDoc.querySelector(AppConfig.SELECTORS.SITE_A.PRODUCT_NAME_INPUT)) {
                        documentContext = iframeDoc;
                        Utils.log('直接分析', '确定操作上下文为iframe');
                        break;
                    }
                }
            } catch (error) { /* 忽略跨域错误 */ }
        }
        if (documentContext === document) {
            Utils.log('直接分析', '确定操作上下文为主文档');
        }

        // 2. 获取标题
        let titleInput = Utils.findElement(AppConfig.SELECTORS.SITE_A.PRODUCT_NAME_INPUT_VARIANTS, documentContext);
        if (!titleInput) {
            Utils.log('直接分析', '找不到标题输入框，尝试备用选择器', 'warn');
            
            // 记录记录当前的选择器，方便调试
            Utils.log('直接分析', `当前尝试的选择器: ${AppConfig.SELECTORS.SITE_A.PRODUCT_NAME_INPUT_VARIANTS}`, 'info');
            
            // 尝试更通用的选择器
            const backupSelectors = [
                'input[placeholder*="商品名称"]',
                'input[name*="GoodsName"]',
                'input[name*="goodsName"]',
                'input.layui-input[placeholder*="名称"]',
                // 查找任何placeholder包含"商品"或"名称"的输入框
                'input[placeholder*="商品"]',
                'input[placeholder*="名称"]',
                // 最后尝试查找任何label文本包含"商品名称"的关联输入框
                'label:contains("商品名称")'
            ];
            
            Utils.log('直接分析', `尝试备用选择器: ${backupSelectors.join(', ')}`, 'info');
            
            // 尝试使用备用选择器
            const backupTitleInput = Utils.findElementWithMultipleSelectors(backupSelectors, documentContext);
            
            if (!backupTitleInput) {
                // 如果仍然找不到，记录当前页面结构以便调试
                Utils.log('直接分析', '使用所有备用选择器仍然找不到标题输入框', 'error');
                Utils.log('直接分析', `页面中的输入框数量: ${documentContext.querySelectorAll('input').length}`, 'info');
                
                // 记录前10个输入框信息，帮助诊断问题
                const allInputs = documentContext.querySelectorAll('input');
                if (allInputs.length > 0) {
                    Utils.log('直接分析', '页面中的部分输入框信息:', 'info');
                    for (let i = 0; i < Math.min(10, allInputs.length); i++) {
                        const input = allInputs[i];
                        Utils.log('直接分析', `输入框 ${i+1}: name="${input.name}", type="${input.type}", id="${input.id}", placeholder="${input.placeholder}"`, 'info');
                    }
                }
                
                // 移除弹窗，改为创建和显示结果容器
                let resultContainer = document.getElementById('direct-ai-result');
                if (!resultContainer) {
                    resultContainer = Utils.createElement('div', {
                        id: 'direct-ai-result',
                        style: AppConfig.STYLES.RESULT_CONTAINER
                    });
                    document.body.appendChild(resultContainer);
                }
                resultContainer.innerHTML = `<div style="color:red;padding:20px;">找不到商品标题输入框，请确保页面元素已加载</div>`;
                resultContainer.style.display = 'block';
                return;
            }
            
            Utils.log('直接分析', '使用备用选择器找到了标题输入框', 'info');
            titleInput = backupTitleInput;
        }
        
        const title = titleInput.value.trim();
        if (!title) {
            Utils.log('直接分析', '标题为空', 'warn');
            // 移除弹窗，改为创建和显示结果容器
            let resultContainer = document.getElementById('direct-ai-result');
            if (!resultContainer) {
                resultContainer = Utils.createElement('div', {
                    id: 'direct-ai-result',
                    style: AppConfig.STYLES.RESULT_CONTAINER
                });
                document.body.appendChild(resultContainer);
            }
            resultContainer.innerHTML = `<div style="color:orange;padding:20px;">商品标题为空，请先填写商品标题</div>`;
            resultContainer.style.display = 'block';
            return;
        }
        Utils.log('直接分析', '获取到标题: ' + title);
        
        // 3. 创建或获取结果容器 (始终在主文档中创建/查找)
        let resultContainer = document.getElementById('direct-ai-result');
        if (!resultContainer) {
            resultContainer = Utils.createElement('div', {
                id: 'direct-ai-result',
                style: AppConfig.STYLES.RESULT_CONTAINER
            });
            // 附加到body或其他合适的位置
            document.body.appendChild(resultContainer);
        }
        
        // 4. 显示加载状态
        resultContainer.innerHTML = `<div style="text-align:center;padding:20px;">${AppConfig.AI.LOADING_TEXT}</div>`;
        resultContainer.style.display = 'block';
        
        // 5. 获取任务数量 (使用封装的方法)
        // 需要一个方法来获取AIFeatureManager的实例，这里暂时假设可以直接调用getTaskCount
        // 这部分逻辑需要调整，确保能访问到正确的taskCount
        // 暂时先用函数内的getTaskCount辅助函数
        const taskCount = _getTaskCountInternal(documentContext); // 调用内部辅助函数
        
        // 6. 获取API配置
        const aiSettings = Utils.getAISettings();
        
        // 7. 准备API请求
        Utils.log('直接分析', '准备调用API');
        Utils.log('直接分析', '使用模型: ' + aiSettings.model);
        Utils.log('直接分析', '使用API Key: ' + (aiSettings.apiKey ? '已设置' : '未设置'));
        
        const prompt = AppConfig.AI.PROMPT_TEMPLATE
            .replace('{title}', title)
            .replace(/\{count\}/g, taskCount);
            
        Utils.log('直接分析', '生成提示词: ' + prompt.substring(0, 100) + '...'); // 避免日志过长
        
        // 更新UI状态 (例如按钮文本或状态指示器)
        updateDirectAnalysisStatus('分析中...', '#1E9FFF');
        
        const requestBody = {
            model: aiSettings.model,
            messages: [{ role: 'user', content: prompt }],
            max_tokens: AppConfig.AI.MAX_TOKENS,
            temperature: AppConfig.AI.TEMPERATURE,
            top_p: AppConfig.AI.TOP_P
        };
        
        Utils.log('直接分析', '请求体: ' + JSON.stringify(requestBody).substring(0, 100) + '...');
        
        // 8. 发送API请求
        try {
            const response = await fetch(aiSettings.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${aiSettings.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            
            Utils.log('直接分析', 'API响应状态: ' + response.status);
            
            if (!response.ok) {
                // 处理常见的HTTP错误
                let errorMsg = `请求失败: ${response.status} ${response.statusText}`;
                if (response.status === 401 || response.status === 403) {
                    errorMsg = `授权错误(${response.status})，请检查API Key是否正确或有权限访问模型`;
                } else if (response.status === 404) {
                    errorMsg = `API端点未找到(404)，请检查API URL`;
                } else if (response.status === 429) {
                    errorMsg = `请求频率过高(429)，请稍后再试`;
                }
                Utils.log('直接分析', errorMsg, 'error');
                throw new Error(errorMsg);
            }
            
            const data = await response.json();
            Utils.log('直接分析', 'API返回数据: ' + JSON.stringify(data).substring(0, 100) + '...');
            
            if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                const content = data.choices[0].message.content.trim();
                Utils.log('直接分析', '解析到的内容: ' + content);
                
                // 解析关键词
                const keywords = _parseKeywordsInternal(content);
                
                // 应用关键词 (传入正确的上下文)
                _applyKeywordsInternal(keywords, documentContext);
                
                // 显示结果
                _showResultsInternal(resultContainer, keywords);
                
                updateDirectAnalysisStatus('完成', 'green');
            } else {
                Utils.log('直接分析', 'API返回数据格式不符合预期', 'error');
                resultContainer.innerHTML = '<div style="color:red;padding:20px;">无法从API返回中解析关键词，请检查API状态或返回格式</div>';
                updateDirectAnalysisStatus('分析失败', 'red');
            }
            
        } catch (error) {
            console.error('直接分析请求失败:', error);
            Utils.log('直接分析', '请求处理异常: ' + error.message, 'error');
            resultContainer.innerHTML = `
                <div style="color:red;padding:20px;">
                    <div>分析失败: ${error.message}</div>
                    <div style="margin-top:10px;font-size:12px;">
                        请检查网络连接、API配置（点击"显示高级设置"），或联系管理员。
                    </div>
                     <button id="direct-close-error-btn" style="padding:5px 12px;margin-top:10px;background:#FF5722;color:white;border:none;border-radius:3px;cursor:pointer;">关闭</button>
                </div>
            `;
             // 添加关闭按钮事件
            const closeBtn = resultContainer.querySelector('#direct-close-error-btn');
            if(closeBtn) {
                closeBtn.onclick = () => { resultContainer.style.display = 'none'; };
            }
            updateDirectAnalysisStatus('分析失败', 'red');
        }
        
    } catch (error) {
        console.error('直接分析执行过程中出错:', error);
        Utils.log('直接分析', '执行异常: ' + error.message, 'error');
        // 移除弹窗，改为在结果容器中显示错误
        let resultContainer = document.getElementById('direct-ai-result');
        if (!resultContainer) {
            resultContainer = Utils.createElement('div', {
                id: 'direct-ai-result',
                style: AppConfig.STYLES.RESULT_CONTAINER
            });
            document.body.appendChild(resultContainer);
        }
        resultContainer.innerHTML = `<div style="color:red;padding:20px;">执行AI分析时发生错误: ${error.message}</div>`;
        resultContainer.style.display = 'block';
        updateDirectAnalysisStatus('执行错误', 'red');
    }
};

/**
 * 内部辅助函数：获取任务数量
 * @param {Document} documentContext - 文档上下文
 * @returns {number} 任务数量
 */
function _getTaskCountInternal(documentContext) {
    // 复用 AIFeatureManager 中的逻辑，但作为独立函数
    try {
        let count = 1; // 默认值
        // 1. 通过任务容器
        const taskContainers = documentContext.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
        if (taskContainers && taskContainers.length > 0) {
            count = taskContainers.length;
            Utils.log('内部辅助', `通过任务容器检测到任务数量: ${count}`);
            return count;
        }
        // 2. 通过关键词标签
        const searchLabels = Array.from(documentContext.querySelectorAll('label')).filter(label =>
            label.textContent.trim().includes('搜索关键词') || label.textContent.trim().includes('搜索词')
        );
        if (searchLabels && searchLabels.length > 0) {
            count = searchLabels.length;
            Utils.log('内部辅助', `通过关键词标签检测到任务数量: ${count}`);
            return count;
        }
        // 3. 通过页面标题文本
        const textElements = documentContext.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_COUNT_TEXT_ELEMENTS);
        for (const element of textElements) {
            const match = element.textContent.match(/第(\d+)单/);
            if (match && match[1]) {
                const num = parseInt(match[1], 10);
                if (!isNaN(num) && num > 0) {
                    count = num;
                    Utils.log('内部辅助', `通过页面文本找到任务数量: ${count}`);
                    return count;
                }
            }
        }
        Utils.log('内部辅助', `使用默认任务数量: ${count}`);
        return count;
    } catch (error) {
        Utils.log('内部辅助', `获取任务数量出错: ${error.message}`, 'error');
        return AppConfig.AI.DEFAULT_TASK_COUNT; // 返回默认值
    }
}

/**
 * 内部辅助函数：解析API返回的内容提取关键词
 * @param {string} content - API返回的内容
 * @returns {string[]} 关键词数组
 */
function _parseKeywordsInternal(content) {
    let keywords = [];
    try {
        // 优先尝试解析严格的JSON格式
        let jsonText = content;
        const jsonMatch = content.match(/\[\s*"[^"]*"(?:\s*,\s*"[^"]*")*\s*\]/);
        if (jsonMatch) {
            jsonText = jsonMatch[0];
            Utils.log('内部辅助', '成功匹配到JSON数组');
            keywords = JSON.parse(jsonText);
        } else {
             Utils.log('内部辅助', '未匹配到严格JSON数组，尝试直接解析');
             // 尝试直接解析，可能包含非JSON文本
             // 移除可能的markdown代码块标记
             jsonText = jsonText.replace(/```json\n?/, '').replace(/```\n?$/, '').trim();
             // 再次尝试解析
             keywords = JSON.parse(jsonText);
        }
        
        if (Array.isArray(keywords) && keywords.length > 0) {
             Utils.log('内部辅助', `成功解析到 ${keywords.length} 个关键词 (JSON)`);
             return keywords.map(k => k.trim()).filter(k => k); // 清理和过滤空关键词
        } else {
            Utils.log('内部辅助', 'JSON解析结果不是有效数组，尝试回退方法', 'warn');
            throw new Error("Parsed result is not a valid array"); // 触发回退
        }
        
    } catch (jsonError) {
        Utils.log('内部辅助', 'JSON解析失败，尝试使用分隔符解析: ' + jsonError);
        // 回退：按常见分隔符（换行、逗号、中文逗号）分割
        keywords = content.split(/[\n,，]/)
            .map(k => k.replace(/^["']|["']$/g, '').trim()) // 移除可能的引号并trim
            .filter(k => k && k.length > 1); // 过滤空或过短的关键词
        Utils.log('内部辅助', `通过分隔符解析到 ${keywords.length} 个关键词`);
    }
    
    return keywords;
}

/**
 * 内部辅助函数：应用生成的关键词到表单的搜索框
 * @param {string[]} keywords - 关键词数组
 * @param {Document} documentContext - 文档上下文
 */
function _applyKeywordsInternal(keywords, documentContext) {
    Utils.log('内部辅助', '开始应用关键词');
    try {
        // 查找所有关键词输入框
        const keywordInputs = _findKeywordInputsInternal(documentContext);
        
        if (keywordInputs.length === 0) {
            Utils.log('内部辅助', '找不到关键词输入框', 'warn');
            // 移除弹窗，改为在结果容器中显示
            const resultContainer = document.getElementById('direct-ai-result');
            if (resultContainer) {
                resultContainer.innerHTML = `<div style="color:red;padding:20px;">未能找到用于填充关键词的输入框</div>`;
            }
            return;
        }
        
        Utils.log('内部辅助', `找到 ${keywordInputs.length} 个关键词输入框`);
        
        // 填充数据
        const countToFill = Math.min(keywords.length, keywordInputs.length);
        Utils.log('内部辅助', `准备填充 ${countToFill} 个关键词`);
        
        for (let i = 0; i < countToFill; i++) {
            const input = keywordInputs[i];
            input.value = keywords[i];
            Utils.log('内部辅助', `填充关键词 ${i+1}: "${keywords[i]}"`);
            
            // 触发必要的事件以确保表单框架识别更改
            input.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
            input.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
             // 尝试触发blur事件，某些框架可能需要
            input.dispatchEvent(new Event('blur', { bubbles: true, cancelable: true }));
        }
        
        Utils.log('内部辅助', `已应用 ${countToFill} 个关键词`);
        // 移除弹窗，使用更简洁的结果显示
        const resultContainer = document.getElementById('direct-ai-result');
        if (resultContainer) {
            const successMsg = `<div style="color:green;padding:5px;margin-top:5px;">已成功填充 ${countToFill} 个关键词</div>`;
            if (resultContainer.querySelector('.keywords-filled-status')) {
                resultContainer.querySelector('.keywords-filled-status').innerHTML = successMsg;
            } else {
                resultContainer.insertAdjacentHTML('beforeend', `<div class="keywords-filled-status">${successMsg}</div>`);
            }
        }
        
    } catch (error) {
        console.error('应用关键词失败:', error);
        Utils.log('内部辅助', `应用关键词时出错: ${error.message}`, 'error');
        // 移除弹窗，改为在结果容器中显示错误
        const resultContainer = document.getElementById('direct-ai-result');
        if (resultContainer) {
            resultContainer.insertAdjacentHTML('beforeend', `<div style="color:red;padding:5px;margin-top:5px;">应用关键词时出错</div>`);
        }
    }
}

/**
 * 内部辅助函数：查找页面中的关键词输入框
 * @param {Document} documentContext - 文档上下文
 * @returns {Element[]} 关键词输入框元素数组
 */
function _findKeywordInputsInternal(documentContext) {
    Utils.log('内部辅助', '开始查找关键词输入框');
    let keywordInputs = [];
    
    // 优先使用配置中的选择器
    const primarySelectors = AppConfig.SELECTORS.SITE_A.KEYWORD_INPUT_VARIANTS;
    keywordInputs = Utils.findAllElements(primarySelectors, documentContext);
    
    if (keywordInputs.length > 0) {
        Utils.log('内部辅助', `通过主要选择器找到 ${keywordInputs.length} 个输入框`);
        return keywordInputs;
    }
    
    // 如果主要选择器失败，尝试通过标签文本查找
    Utils.log('内部辅助', '主要选择器未找到，尝试通过标签文本查找');
    const searchLabels = Array.from(documentContext.querySelectorAll('label')).filter(label =>
        label.textContent.trim().includes('搜索关键词') ||
        label.textContent.trim().includes('搜索词')
    );
    
    const inputsFromLabels = [];
    for (const label of searchLabels) {
        // 查找与标签关联的输入框 (for属性或父容器内的input)
        const inputId = label.getAttribute('for');
        if (inputId) {
            const input = documentContext.getElementById(inputId);
            if (input && input.tagName === 'INPUT') {
                inputsFromLabels.push(input);
                continue;
            }
        }
        // 查找父容器内的第一个input
        const parent = label.parentElement;
        if (parent) {
            const input = parent.querySelector('input[type="text"]');
            if (input) {
                inputsFromLabels.push(input);
            } else {
                // 尝试更复杂的查找，如父级的父级
                const formItem = label.closest('.layui-form-item');
                if (formItem) {
                    const input = formItem.querySelector('input[type="text"]');
                    if (input) inputsFromLabels.push(input);
                }
            }
        }
    }
    
    if (inputsFromLabels.length > 0) {
         // 去重
        keywordInputs = [...new Set(inputsFromLabels)];
        Utils.log('内部辅助', `通过标签文本找到 ${keywordInputs.length} 个输入框`);
        return keywordInputs;
    }
    
    // 如果仍然失败，使用最后的备用选择器（最宽松）
    Utils.log('内部辅助', '标签文本查找失败，尝试最后的备用选择器');
    keywordInputs = Utils.findAllElements(AppConfig.SELECTORS.SITE_A.KEYWORD_INPUT_FALLBACK, documentContext);
    
    if (keywordInputs.length > 0) {
        Utils.log('内部辅助', `通过备用选择器找到 ${keywordInputs.length} 个输入框`);
        return keywordInputs;
    }
    
    Utils.log('内部辅助', '未能找到任何关键词输入框', 'warn');
    return [];
}

/**
 * 内部辅助函数：显示结果容器
 * @param {Element} container - 结果容器元素
 * @param {string[]} keywords - 关键词数组
 */
function _showResultsInternal(container, keywords) {
    Utils.log('内部辅助', '显示结果容器');
    container.innerHTML = `
        <h3 style="margin:0 0 10px 0;color:#333;border-bottom:1px solid #eee;padding-bottom:8px;">
            ${AppConfig.AI.RESULT_HEADER} (${keywords.length}个)
        </h3>
        <div style="margin:10px 0;color:#1E9FFF;line-height:1.6;">
            ${keywords.map(k => `<span style="display:inline-block; background:#eaf6ff; border:1px solid #cfe9ff; padding: 2px 6px; margin: 3px; border-radius: 3px;">${k}</span>`).join('')}
        </div>
        <div style="margin-top:15px; text-align:right;">
            <button id="direct-copy-btn" style="padding:5px 12px;background:#009688;color:white;border:none;border-radius:3px;cursor:pointer;margin-right:5px;">复制全部</button>
            <button id="direct-close-btn" style="padding:5px 12px;background:#FF5722;color:white;border:none;border-radius:3px;cursor:pointer;">关闭</button>
        </div>
    `;
    container.style.display = 'block';
    
    // 添加按钮事件
    const copyBtn = container.querySelector('#direct-copy-btn');
    const closeBtn = container.querySelector('#direct-close-btn');
    
    if (copyBtn) {
        copyBtn.onclick = () => {
            const textToCopy = keywords.join(', '); // 使用逗号分隔复制
            navigator.clipboard.writeText(textToCopy)
                .then(() => {
                    Utils.log('内部辅助', '关键词已复制');
                    copyBtn.textContent = '已复制!';
                    setTimeout(() => { copyBtn.textContent = '复制全部'; }, 1500);
                })
                .catch(err => {
                     console.error('复制失败:', err);
                     alert('复制失败，请手动复制。');
                 });
        };
    }
    
    if (closeBtn) {
        closeBtn.onclick = () => { container.style.display = 'none'; };
    }
}

/**
 * 更新直接分析状态的UI元素
 * @param {string} statusText - 状态文本
 * @param {string} color - CSS颜色值
 */
function updateDirectAnalysisStatus(statusText, color) {
    // 这个函数需要知道具体更新哪个UI元素，
    // 最好由调用者（如 UIManager）来处理UI更新
    Utils.log('状态更新', `状态: ${statusText}`, 'info');
    
    // 示例：尝试更新一个假设存在的状态元素
    const statusElement = document.getElementById('ai-direct-analysis-status');
    if (statusElement) {
        statusElement.textContent = `AI分析状态: ${statusText}`;
        statusElement.style.color = color;
    } else {
         // 如果特定元素不存在，可以在控制台记录
         Utils.log('状态更新', `未能找到状态UI元素 (ai-direct-analysis-status)`);
    }
}
