class ZhiyiImageCollector {
    constructor() {
        this.imageUrls = [];
        this.isInitialized = false;
        this.initAttempts = 0;
        this.maxAttempts = 50;
        this.selectedImages = new Set();
    }

    // 等待元素出现
    async waitForElement(selector, timeout = 10000) {
        console.log(`等待元素出现: ${selector}`);
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`找到元素: ${selector}`);
                return element;
            }
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        console.warn(`等待元素超时: ${selector}`);
        return null;
    }

    // 检查并添加按钮
    async checkAndAddButton() {
        console.log(`尝试添加按钮，第 ${this.initAttempts + 1} 次`);
        
        // 尝试不同的选择器
        const selectors = [
            '.zk-image-detail-actions',
            '.picture-wrapper',
            '.zk-image-detail-picture'
        ];

        for (const selector of selectors) {
            const container = await this.waitForElement(selector, 2000);
            if (container) {
                console.log(`找到容器: ${selector}`);
                this.addControlPanel(container);
                return true;
            }
        }

        this.initAttempts++;
        if (this.initAttempts < this.maxAttempts) {
            setTimeout(() => this.checkAndAddButton(), 200);
        } else {
            console.error('添加按钮失败，超过最大尝试次数');
        }
        return false;
    }

    // 添加控制面板
    addControlPanel(container) {
        console.log('添加控制面板');
        const controlPanel = document.createElement('div');
        controlPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
            flex-direction: column;
        `;

        // 全选按钮
        const selectAllButton = document.createElement('button');
        selectAllButton.innerHTML = '全选';
        selectAllButton.style.cssText = `
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 120px;
        `;

        // 转移按钮
        const transferButton = document.createElement('button');
        transferButton.style.cssText = `
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 120px;
        `;

        // 下载按钮
        const downloadButton = document.createElement('button');
        downloadButton.innerHTML = '下载图片';
        downloadButton.style.cssText = `
            background: #FF4D4F;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 120px;
        `;

        // 复制图像按钮
        const copyButton = document.createElement('button');
        copyButton.innerHTML = '复制图像';
        copyButton.style.cssText = `
            background: #722ED1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 120px;
        `;

        // 更新按钮文本的函数
        const updateButtonText = () => {
            transferButton.innerHTML = `转移图片 (${this.selectedImages.size})`;
        };

        // 添加事件监听
        selectAllButton.addEventListener('click', () => this.toggleSelectAll());
        transferButton.addEventListener('click', () => this.collectImages());
        downloadButton.addEventListener('click', () => this.downloadImages());
        copyButton.addEventListener('click', () => this.copyImage());

        controlPanel.appendChild(selectAllButton);
        controlPanel.appendChild(transferButton);
        controlPanel.appendChild(downloadButton);
        controlPanel.appendChild(copyButton);

        document.body.appendChild(controlPanel);
        this.updateSelectedCount = updateButtonText;
        updateButtonText();

        // 添加拖动功能
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        // 添加拖动手柄
        const dragHandle = document.createElement('div');
        dragHandle.style.cssText = `
            width: 100%;
            height: 10px;
            background: #f0f0f0;
            border-radius: 4px 4px 0 0;
            cursor: move;
            margin-bottom: 5px;
        `;

        controlPanel.insertBefore(dragHandle, controlPanel.firstChild);

        dragHandle.addEventListener('mousedown', dragStart);

        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === dragHandle) {
                isDragging = true;
            }
        }

        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                setTranslate(currentX, currentY, controlPanel);
            }
        }

        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        }

        function setTranslate(xPos, yPos, el) {
            el.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
        }
    }

    // 切换全选状态
    toggleSelectAll() {
        const thumbs = document.querySelectorAll('.ins-detail-thumb');
        const isAllSelected = this.selectedImages.size === thumbs.length;

        thumbs.forEach(thumb => {
            const imgUrl = this.getImageUrl(thumb);
            if (isAllSelected) {
                this.selectedImages.delete(imgUrl);
                thumb.style.border = 'none';
            } else {
                this.selectedImages.add(imgUrl);
                thumb.style.border = '2px solid #4CAF50';
            }
        });

        this.updateSelectedCount();
    }

    // 从缩略图获取原图URL
    getImageUrl(thumb) {
        const style = thumb.style.backgroundImage;
        let url = style.match(/url\(['"]?(.*?)['"]?\)/)[1];
        return url.split('?')[0]; // 移除查询参数以获取原图URL
    }

    // 添加缩略图点击事件
    addThumbClickEvents() {
        console.log('添加缩略图点击事件');
        const observer = new MutationObserver((mutations, obs) => {
            const thumbs = document.querySelectorAll('.ins-detail-thumb');
            thumbs.forEach(thumb => {
                if (!thumb.dataset.hasClickEvent) {
                    thumb.dataset.hasClickEvent = 'true';
                    
                    // 添加点击事件
                    thumb.addEventListener('click', (e) => {
                        const imgUrl = this.getImageUrl(thumb);
                        
                        // 切换选中状态
                        if (this.selectedImages.has(imgUrl)) {
                            this.selectedImages.delete(imgUrl);
                            thumb.style.border = 'none';
                        } else {
                            this.selectedImages.add(imgUrl);
                            thumb.style.border = '2px solid #4CAF50';
                            thumb.style.borderRadius = '4px';
                            thumb.style.boxSizing = 'border-box';
                        }
                        
                        this.updateSelectedCount();
                        
                        // 不阻止事件冒泡，允许查看大图
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 下载选中的图片
    async downloadImages() {
        if (this.selectedImages.size === 0) {
            alert('请至少选择一张图片');
            return;
        }

        try {
            // 创建下载进度提示
            const progressDiv = document.createElement('div');
            progressDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 10000;
            `;
            document.body.appendChild(progressDiv);
            progressDiv.textContent = '正在下载...';

            // 使用 chrome.downloads API 下载图片
            const downloads = Array.from(this.selectedImages).map((url, index) => {
                return new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        type: 'downloadImage',
                        url: url,
                        filename: `image_${index + 1}.jpg`
                    }, (response) => {
                        if (response && response.success) {
                            resolve();
                        } else {
                            reject(new Error('下载失败'));
                        }
                    });
                });
            });

            await Promise.all(downloads);
            document.body.removeChild(progressDiv);
            
        } catch (error) {
            console.error('下载失败:', error);
            alert('下载失败，请重试');
        }
    }

    // 收集选中的图片
    async collectImages() {
        if (this.selectedImages.size === 0) {
            alert('请至少选择一张图片');
            return;
        }

        const imageGroup = {
            images: Array.from(this.selectedImages)
        };

        chrome.runtime.sendMessage({
            type: 'processReviews',
            reviews: [imageGroup]
        });
    }

    // 复制图像到剪贴板
    async copyImage() {
        if (this.selectedImages.size === 0) {
            alert('请选择至少一张图片进行复制');
            return;
        }

        try {
            // 创建复制进度提示
            const progressDiv = document.createElement('div');
            progressDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 10000;
            `;
            document.body.appendChild(progressDiv);
            progressDiv.textContent = '准备复制图像...';
            
            const imageUrls = Array.from(this.selectedImages);
            
            // 如果选择了多张图片，提示用户只能复制第一张
            if (imageUrls.length > 1) {
                progressDiv.textContent = '一次只能复制一张图片，将复制第一张选中的图片';
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
            
            // 开始加载图像
            progressDiv.textContent = '正在加载图像...';
            
            // 使用Fetch API获取图像以解决跨域问题
            const response = await fetch(imageUrls[0], {
                mode: 'cors',  // 尝试跨域请求
                cache: 'no-cache'
            });
            
            if (!response.ok) {
                throw new Error('无法加载图像');
            }
            
            // 获取图像Blob
            const blob = await response.blob();
            
            // 创建一个图像对象
            const imgObj = new Image();
            const imgUrl = URL.createObjectURL(blob);
            
            // 等待图像加载
            await new Promise((resolve, reject) => {
                imgObj.onload = resolve;
                imgObj.onerror = reject;
                imgObj.src = imgUrl;
                
                // 设置超时
                setTimeout(resolve, 3000);
            });
            
            // 创建Canvas并绘制图像
            const canvas = document.createElement('canvas');
            canvas.width = imgObj.naturalWidth || 800; // 使用原始宽度，如果获取不到则使用默认值
            canvas.height = imgObj.naturalHeight || 600;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(imgObj, 0, 0);
            
            // 释放对象URL
            URL.revokeObjectURL(imgUrl);
            
            progressDiv.textContent = '复制中...';
            
            try {
                // 尝试使用现代剪贴板API
                canvas.toBlob(async (canvasBlob) => {
                    try {
                        // 尝试使用Clipboard API
                        const item = new ClipboardItem({
                            [canvasBlob.type]: canvasBlob
                        });
                        
                        await navigator.clipboard.write([item]);
                        progressDiv.textContent = '复制成功!';
                        setTimeout(() => document.body.removeChild(progressDiv), 1500);
                    } catch (clipboardError) {
                        console.error('Clipboard API失败:', clipboardError);
                        
                        // 使用备用方法：创建临时图像并使用execCommand
                        try {
                            // 将Canvas转为img元素
                            const tempImg = document.createElement('img');
                            tempImg.src = canvas.toDataURL('image/png');
                            
                            // 使其不可见但存在于DOM中
                            tempImg.style.position = 'fixed';
                            tempImg.style.left = '-9999px';
                            document.body.appendChild(tempImg);
                            
                            // 等待图像加载
                            await new Promise(resolve => {
                                tempImg.onload = resolve;
                                setTimeout(resolve, 1000); // 超时以防万一
                            });
                            
                            // 创建范围和选择
                            const range = document.createRange();
                            range.selectNode(tempImg);
                            
                            // 清除现有选择并添加新选择
                            const selection = window.getSelection();
                            selection.removeAllRanges();
                            selection.addRange(range);
                            
                            // 执行复制命令
                            const success = document.execCommand('copy');
                            
                            // 清理
                            selection.removeAllRanges();
                            document.body.removeChild(tempImg);
                            
                            if (success) {
                                progressDiv.textContent = '复制成功!';
                            } else {
                                progressDiv.textContent = '复制失败，请重试';
                            }
                            
                            setTimeout(() => document.body.removeChild(progressDiv), 1500);
                        } catch (e) {
                            console.error('备用复制方法失败:', e);
                            progressDiv.textContent = '复制失败，请重试';
                            setTimeout(() => document.body.removeChild(progressDiv), 1500);
                        }
                    }
                }, 'image/png');
            } catch (e) {
                console.error('Canvas转换失败:', e);
                progressDiv.textContent = '复制失败，请重试';
                setTimeout(() => document.body.removeChild(progressDiv), 1500);
            }
        } catch (error) {
            console.error('复制图像失败:', error);
            alert('复制失败，请重试');
        }
    }

    // 初始化
    initialize() {
        console.log('初始化知衣图片采集器');
        // 先加载 JSZip
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js';
        document.head.appendChild(script);
        
        this.checkAndAddButton();
        this.addThumbClickEvents();
    }
}

// 确保脚本执行
console.log('知衣内容脚本开始执行');
const collector = new ZhiyiImageCollector();

// 等待页面加载完成
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => collector.initialize());
} else {
    collector.initialize();
} 