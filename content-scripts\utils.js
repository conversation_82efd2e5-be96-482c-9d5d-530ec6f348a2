/**
 * 工具函数文件
 * 集中管理通用工具函数、DOM操作、存储等辅助方法
 */

// 工具函数命名空间
window.Utils = {
    /**
     * 等待元素出现
     * @param {string} selector - 要等待的元素选择器
     * @param {number} timeout - 超时时间（毫秒）
     * @param {number} interval - 检查间隔（毫秒）
     * @returns {Promise<Element>} - 找到的元素
     */
    waitForElement: function(selector, timeout = 10000, interval = 100) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`等待元素 ${selector} 超时`));
                    return;
                }
                
                setTimeout(checkElement, interval);
            };
            
            checkElement();
        });
    },
    
    /**
     * 创建DOM元素
     * @param {string} tag - 元素标签
     * @param {Object} attrs - 属性对象
     * @param {string|Element} content - 内容或子元素
     * @param {Object} eventListeners - 事件监听器对象 {click: function() {}, ...}
     * @returns {Element} - 创建的元素
     */
    createElement: function(tag, attrs = {}, content = '', eventListeners = {}) {
        const element = document.createElement(tag);
        
        // 设置属性
        Object.entries(attrs).forEach(([key, value]) => {
            if (key === 'style' && typeof value === 'string') {
                element.style.cssText = value;
            } else {
                element.setAttribute(key, value);
            }
        });
        
        // 设置内容
        if (content) {
            if (typeof content === 'string') {
                element.innerHTML = content;
            } else if (content instanceof Element) {
                element.appendChild(content);
            }
        }
        
        // 添加事件监听器
        Object.entries(eventListeners).forEach(([event, listener]) => {
            element.addEventListener(event, listener);
        });
        
        return element;
    },
    
    /**
     * 查找元素，支持多个选择器尝试
     * @param {string|string[]} selectors - 选择器或选择器数组
     * @param {Element} parent - 父元素，默认为document
     * @returns {Element|null} - 找到的第一个元素或null
     */
    findElement: function(selectors, parent = document) {
        if (!selectors) return null;
        
        const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
        
        for (const selector of selectorArray) {
            try {
                const element = parent.querySelector(selector);
                if (element) return element;
            } catch (error) {
                console.error(`选择器语法错误: ${selector}`, error);
            }
        }
        
        return null;
    },
    
    /**
     * 获取所有匹配的元素
     * @param {string|string[]} selectors - 选择器或选择器数组
     * @param {Element} parent - 父元素，默认为document
     * @returns {Element[]} - 找到的所有元素数组
     */
    findAllElements: function(selectors, parent = document) {
        if (!selectors) return [];
        
        const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
        const results = [];
        
        for (const selector of selectorArray) {
            try {
                const elements = Array.from(parent.querySelectorAll(selector));
                results.push(...elements);
            } catch (error) {
                console.error(`选择器语法错误: ${selector}`, error);
            }
        }
        
        return results;
    },
    
    /**
     * 获取AI设置
     * @returns {Object} AI配置对象
     */
    getAISettings: function() {
        // 从localStorage获取设置，如果没有则使用默认值
        const savedSettings = localStorage.getItem('aiAnalysisSettings');
        if (savedSettings) {
            try {
                return JSON.parse(savedSettings);
            } catch (error) {
                console.error('解析保存的AI设置失败:', error);
            }
        }
        
        // 默认设置
        return {
            apiUrl: window.AppConfig.AI.API_URL,
            model: window.AppConfig.AI.MODEL,
            apiKey: window.AppConfig.AI.API_KEY
        };
    },
    
    /**
     * 保存AI设置
     * @param {Object} settings - 要保存的设置对象
     * @returns {boolean} 是否保存成功
     */
    saveAISettings: function(settings) {
        try {
            localStorage.setItem('aiAnalysisSettings', JSON.stringify(settings));
            console.log('AI设置已保存');
            return true;
        } catch (error) {
            console.error('保存AI设置失败:', error);
            return false;
        }
    },
    
    /**
     * 延迟执行函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    delay: function(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    /**
     * 安全地执行函数，捕获并记录错误
     * @param {Function} fn - 要执行的函数
     * @param {string} errorMsg - 错误消息前缀
     * @param {any} defaultValue - 出错时返回的默认值
     * @returns {any} 函数返回值或默认值
     */
    safeExecute: function(fn, errorMsg, defaultValue = null) {
        try {
            return fn();
        } catch (error) {
            console.error(`${errorMsg}: ${error.message}`);
            return defaultValue;
        }
    },
    
    /**
     * 格式化调试信息
     * @param {string} source - 来源标识
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (log|info|warn|error)
     */
    log: function(source, message, type = 'log') {
        if (!window.AppConfig.DEBUG) return;
        
        // 优化1: 过滤大部分普通日志，只保留关键信息
        if (type === 'log') {
            // 只输出包含特定关键词的普通日志
            const importantKeywords = ['初始化', '完成', '成功', '失败', '错误', '开始处理', '结束处理'];
            const isImportant = importantKeywords.some(keyword => message.includes(keyword));
            if (!isImportant) return; // 过滤掉非重要日志
        }
        
        // 优化2: 对于info级别的日志也进行筛选
        if (type === 'info') {
            // 保留与状态变化相关的重要信息日志
            const infoKeywords = ['初始化', '完成', '成功', '配置', '设置', '创建'];
            const isImportantInfo = infoKeywords.some(keyword => message.includes(keyword));
            if (!isImportantInfo) return;
        }
        
        const prefix = `【${source}】`;
        const output = `${prefix} ${message}`;
        
        switch (type) {
            case 'error':
                console.error(output);
                break;
            case 'warn':
                console.warn(output);
                break;
            case 'info':
                console.info(output);
                break;
            default:
                console.log(output);
        }
    },
    
    /**
     * 提取淘宝商品ID
     * @param {string} url - 淘宝商品URL
     * @returns {string|null} 商品ID或null
     */
    extractTaobaoItemId: function(url) {
        if (!url) return null;

        try {
            // 1. 预处理URL
            let processedUrl = url.trim();
            while (processedUrl.includes('%25')) {
                try {
                    processedUrl = decodeURIComponent(processedUrl);
                } catch (e) { break; }
            }

            // 2. 正则表达式提取
            const idPatterns = [
                /id=(\d+)/i,
                /item\.htm.*?id=(\d+)/i,
                /item\/(\d+)\.htm/i,
                /itemId=(\d+)/i,
                /item_id=(\d+)/i,
                /(?:taobao\.com|tmall\.com).*?\/(\d{10,})/i
            ];
            for (const pattern of idPatterns) {
                const match = processedUrl.match(pattern);
                if (match && match[1] && match[1].length >= 5) {
                    return match[1];
                }
            }

            // 3. URL API 解析
            try {
                const parsedUrl = new URL(processedUrl);
                const itemId = parsedUrl.searchParams.get('id');
                if (itemId) return itemId;
            } catch (e) { /* Continue */ }

            // 4. 查找长数字序列
            const longDigitMatches = processedUrl.match(/(\d{10,12})/g);
            if (longDigitMatches && longDigitMatches.length > 0) {
                const potentialIds = longDigitMatches.filter(id => id.length >= 10 && id.length <= 12);
                return potentialIds.length > 0 ? potentialIds[0] : longDigitMatches[0];
            }

            return null;
        } catch (error) {
            this.log('Utils', `提取商品ID出错: ${error.message}`, 'error');
            return null;
        }
    },
    
    /**
     * 使用多个选择器查找元素
     * @param {string[]} selectors - 要尝试的选择器数组
     * @param {Document|Element} context - 搜索上下文
     * @returns {Element|null} - 找到的元素或null
     */
    findElementWithMultipleSelectors: function(selectors, context = document) {
        if (!selectors || !selectors.length) return null;
        
        for (const selector of selectors) {
            try {
                // 处理包含:contains()伪选择器的情况
                if (selector.includes(':contains(')) {
                    // 例如: label:contains("商品名称")
                    const parts = selector.match(/([a-z0-9]+):contains\("(.+?)"\)/i);
                    if (parts && parts.length >= 3) {
                        const [_, tagName, searchText] = parts;
                        // 查找所有该标签元素
                        const elements = context.querySelectorAll(tagName);
                        // 查找文本匹配的元素
                        for (const element of elements) {
                            if (element.textContent.includes(searchText)) {
                                // 如果是label，尝试找到关联的input
                                if (tagName.toLowerCase() === 'label') {
                                    // 通过for属性
                                    const forAttr = element.getAttribute('for');
                                    if (forAttr) {
                                        const input = context.getElementById(forAttr);
                                        if (input && input.tagName === 'INPUT') return input;
                                    }
                                    
                                    // 通过父元素查找
                                    const parent = element.parentElement;
                                    if (parent) {
                                        const input = parent.querySelector('input');
                                        if (input) return input;
                                    }
                                    
                                    // 查找兄弟元素
                                    let sibling = element.nextElementSibling;
                                    while (sibling) {
                                        if (sibling.tagName === 'INPUT') return sibling;
                                        sibling = sibling.nextElementSibling;
                                    }
                                } else {
                                    // 其他标签直接返回
                                    return element;
                                }
                            }
                        }
                    }
                } else {
                    // 普通选择器
                    const element = context.querySelector(selector);
                    if (element) return element;
                }
            } catch (error) {
                this.log('Utils', `选择器 "${selector}" 处理失败: ${error.message}`, 'warn');
            }
        }
        
        return null;
    },
    
    /**
     * 显示全局通知
     */
    showGlobalNotification: function() {
        // Implementation of showGlobalNotification function
    }
};
