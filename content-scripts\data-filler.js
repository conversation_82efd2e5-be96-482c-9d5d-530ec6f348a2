/**
 * 数据填充模块
 * 负责将从淘宝等源获取的数据填充到网站A的表单中
 */

// 数据填充器类 - 整合了 site-a-content.js 中的逻辑
window.DataFiller = class DataFiller {
    constructor() {
        this.goodsCount = 0; // 记录已填充的商品数量
        this.floatingButton = null; // 悬浮按钮元素
        // 确保 CommentManager 实例被创建
        if (typeof CommentManager !== 'undefined') {
            this.commentManager = new CommentManager();
        } else {
            console.error("CommentManager 类未定义！请确保 comment-manager.js 已加载。");
            // 提供一个空的 commentManager 以避免后续错误
            this.commentManager = {
                initialize: async () => {},
                getRandomComment: async () => '',
                reset: () => {},
                isRandomEnabled: () => false,
                toggleRandom: () => false
            };
        }
        this.lastFilledIndex = -1; // 记录上次填充到的输入框索引
        this.targetUrlPattern = AppConfig.SITES.SITE_A.URL_PATTERN;
        this.fillButton = null; // 保存一键填充按钮的引用
    }
    
    /**
     * 初始化数据填充器
     */
    async initialize() {
         Utils.log('数据填充', '初始化数据填充器');
        
        // 检查是否为目标页面
        if (!window.location.href.includes(this.targetUrlPattern)) {
             Utils.log('数据填充', '非目标页面，不初始化填充功能');
            return;
        }
        
        // 确保 commentManager 存在且已初始化
        if (this.commentManager && typeof this.commentManager.initialize === 'function') {
            await this.commentManager.initialize(); // 初始化评论管理器
        }
        this.createFloatingButton(); // 创建悬浮按钮
        this.setupMessageListener(); // 设置消息监听器
        this.updateGoodsCount(); // 初始化商品计数
        
        Utils.log('数据填充', '数据填充器初始化完成');
    }
    
    /**
     * 创建悬浮按钮集合
     */
    createFloatingButton() {
         Utils.log('数据填充', '创建悬浮按钮');
         if (document.getElementById('floating-fill-button-container')) {
             Utils.log('数据填充', '悬浮按钮已存在');
            return;
        }
        
         const container = Utils.createElement('div', {
             id: 'floating-fill-button-container',
             style: AppConfig.STYLES.FLOATING_BUTTON_CONTAINER
         });
         
         // 一键填充按钮
         this.fillButton = this.createButton('🚀', '一键填充 (Ctrl+Shift+F)', async () => {
             Utils.log('填充按钮', '一键填充被点击');
             await this.fillInputsFromStorage();
         });
         container.appendChild(this.fillButton);
         
         // 重置评论按钮
         const resetButton = this.createButton('🔄', '重置评论库', () => {
             Utils.log('填充按钮', '重置评论库被点击');
             if (this.commentManager && typeof this.commentManager.reset === 'function') {
                 this.commentManager.reset();
                 alert('评论库已重置');
             }
         });
         container.appendChild(resetButton);
         
         // 启用/禁用随机评论按钮
         let isRandomInitiallyEnabled = false;
         if (this.commentManager && typeof this.commentManager.isRandomEnabled === 'function') {
            isRandomInitiallyEnabled = this.commentManager.isRandomEnabled();
         }
         const toggleButton = this.createButton(
             isRandomInitiallyEnabled ? '✅ 随机' : '❌ 随机', 
             isRandomInitiallyEnabled ? '当前启用随机评论，点击禁用' : '当前禁用随机评论，点击启用',
             () => {
                 if (this.commentManager && typeof this.commentManager.toggleRandom === 'function') {
                     const enabled = this.commentManager.toggleRandom();
                     toggleButton.textContent = enabled ? '✅ 随机' : '❌ 随机';
                     toggleButton.title = enabled ? '当前启用随机评论，点击禁用' : '当前禁用随机评论，点击启用';
                     Utils.log('填充按钮', `随机评论已 ${enabled ? '启用' : '禁用'}`);
                 }
             }
         );
         container.appendChild(toggleButton);
         
         document.body.appendChild(container);
         this.floatingButton = container;
         
         // 添加快捷键
         document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                Utils.log('快捷键', 'Ctrl+Shift+F 检测到');
                if (this.fillButton && !this.fillButton.disabled) {
                    this.fillButton.click(); // 模拟点击填充按钮
                }
            }
        });
    }
    
    /**
     * 创建单个按钮
     * @param {string} icon - 按钮图标或文本
     * @param {string} title - 按钮提示文本
     * @param {Function} onClick - 点击事件处理函数
     * @returns {Element} - 创建的按钮元素
     */
    createButton(icon, title, onClick) {
        // 使用 Utils.createElement 创建按钮
        const button = Utils.createElement('button', {
            style: AppConfig.STYLES.FLOATING_BUTTON,
            title: title,
            'data-tooltip': title // 使用 data-tooltip 存储提示信息
        });
        button.innerHTML = icon; // 设置按钮内容

        // 添加事件监听器
        button.addEventListener('click', onClick);

        // 添加鼠标悬停事件来显示/隐藏自定义提示
        let tooltipElement = null;
        button.addEventListener('mouseover', (e) => {
            // 移除旧提示
            const existingTooltip = document.getElementById('filler-tooltip');
            if (existingTooltip) existingTooltip.remove();

            // 创建新提示
            tooltipElement = Utils.createElement('div', {
                id: 'filler-tooltip',
                style: AppConfig.STYLES.TOOLTIP,
                content: e.target.getAttribute('data-tooltip') || title
            });
            document.body.appendChild(tooltipElement);

            // 定位提示
            const rect = e.target.getBoundingClientRect();
            tooltipElement.style.left = `${rect.left + window.scrollX}px`; // 考虑页面滚动
            tooltipElement.style.top = `${rect.bottom + window.scrollY + 5}px`;
        });

        button.addEventListener('mouseout', () => {
            if (tooltipElement) {
                tooltipElement.remove();
                tooltipElement = null;
            }
        });

        return button;
    }
    
    /**
     * 设置消息监听器，接收来自 background 的数据
     */
    setupMessageListener() {
         Utils.log('数据填充', '设置消息监听器');
         chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
             if (message.type === 'fillData') {
                 Utils.log('消息监听', '收到填充数据请求:', message.data);
                 this.storeDataForFilling(message.data); // 先存储数据
                 sendResponse({ success: true });
                 // 更新按钮状态提示有新数据
                 if (this.fillButton) {
                      this.fillButton.innerHTML = '🟢'; // 按钮变绿提示有数据
                      this.fillButton.title = '收到新数据，点击填充 (Ctrl+Shift+F)';
                 }
                 return true; // 异步响应
             }
             return false; // 其他消息不处理
         });
    }

    /**
     * 将待填充的数据存储到 sessionStorage
     * @param {Object} data - { url: string, price: string }
     */
    storeDataForFilling(data) {
        try {
            sessionStorage.setItem('pendingFillData', JSON.stringify(data));
            Utils.log('数据存储', '数据已暂存到 sessionStorage');
        } catch (error) {
            console.error('存储待填充数据失败:', error);
            Utils.log('数据存储', '存储数据失败', 'error');
        }
    }
    
    /**
     * 从 sessionStorage 读取待填充的数据
     * @returns {Object|null} - 返回存储的数据，或 null
     */
    readDataForFilling() {
        try {
            const storedData = sessionStorage.getItem('pendingFillData');
            if (storedData) {
                sessionStorage.removeItem('pendingFillData'); // 读取后清除
                Utils.log('数据读取', '已从 sessionStorage 读取并清除数据');
                return JSON.parse(storedData);
            } else {
                Utils.log('数据读取', 'sessionStorage 中无待填充数据', 'warn');
                return null;
            }
        } catch (error) {
            console.error('读取待填充数据失败:', error);
            Utils.log('数据读取', '读取数据失败', 'error');
            sessionStorage.removeItem('pendingFillData'); // 出错也清除
            return null;
        }
    }
    
    /**
     * 从存储中读取数据并填充到输入框
     */
    async fillInputsFromStorage() {
        const dataToFill = this.readDataForFilling();
        if (!dataToFill) {
            alert('没有待填充的数据。请先从淘宝商品页复制链接和价格。');
            if (this.fillButton) {
                this.fillButton.innerHTML = '🚀'; // 恢复按钮图标
                this.fillButton.title = '一键填充 (Ctrl+Shift+F)';
            }
            return;
        }
        
        Utils.log('数据填充', '开始使用存储的数据填充表单');
        if (this.fillButton) {
            this.fillButton.innerHTML = '⏳'; // 填充中状态
            this.fillButton.disabled = true;
        }
        
        try {
            await this.fillData(dataToFill);
            Utils.log('数据填充', '表单填充成功');
             // 可以在这里给用户一个成功的提示
            
        } catch (error) {
            console.error('使用存储数据填充失败:', error);
            Utils.log('数据填充', `填充失败: ${error.message}`, 'error');
            alert(`填充数据时出错: ${error.message}`);
        } finally {
            // 恢复按钮状态
            if (this.fillButton) {
                this.fillButton.innerHTML = '🚀';
                this.fillButton.title = '一键填充 (Ctrl+Shift+F)';
                this.fillButton.disabled = false;
            }
        }
    }
    
    /**
     * 将数据填充到页面的输入框
     * @param {Object} data - { url: string, price: string }
     */
    async fillData(data) {
         Utils.log('数据填充', '执行填充操作', data);
         this.updateGoodsCount(); // 更新当前商品数量
         
         // 1. 查找下一个空的URL输入框
         const urlInput = this.getNextEmptyUrlInput();
         if (!urlInput) {
             Utils.log('数据填充', '未找到空的URL输入框，尝试添加新任务', 'warn');
             
             // 尝试自动点击添加按钮（如果存在）
             const success = this.clickAddButton();
             if (success) {
                 Utils.log('数据填充', '已点击添加按钮，等待新行出现后重试');
                 await Utils.delay(1500); // 等待DOM更新
                 const newUrlInput = this.getNextEmptyUrlInput();
                 if (newUrlInput) {
                     await this.fillDataToInput(newUrlInput, data);
                 } else {
                      throw new Error('添加新任务后仍未找到空的URL输入框');
                 }
             } else {
                 throw new Error('所有URL输入框均已填充，且无法自动添加新任务');
             }
             
         } else {
              // 2. 填充找到的输入框及其关联字段
              await this.fillDataToInput(urlInput, data);
         }
         
         this.updateGoodsCount(); // 再次更新商品计数
         Utils.log('数据填充', `填充完成，当前商品数: ${this.goodsCount}`);
    }
    
    /**
     * 将数据填充到指定的URL输入框及其关联字段
     * @param {Element} urlInput - URL输入框元素
     * @param {Object} data - { url: string, price: string }
     */
    async fillDataToInput(urlInput, data) {
         Utils.log('填充到输入框', '开始填充数据到特定输入框', urlInput);
         const taskContainer = urlInput.closest(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
         if (!taskContainer) {
             Utils.log('填充到输入框', '无法找到URL输入框所属的任务容器', 'error');
             throw new Error('内部错误：无法定位任务容器');
         }
         
         // 填充URL
         urlInput.value = data.url;
         this.triggerInputEvents(urlInput);
         Utils.log('填充到输入框', `URL已填充: ${data.url}`);
         
         // 查找并填充价格输入框
         const priceInput = taskContainer.querySelector(AppConfig.SELECTORS.SITE_A.PRICE_INPUT);
         if (priceInput) {
             priceInput.value = data.price;
             this.triggerInputEvents(priceInput);
             Utils.log('填充到输入框', `价格已填充: ${data.price}`);
         } else {
             Utils.log('填充到输入框', '未找到价格输入框', 'warn');
         }
         
         // 查找并填充关键词输入框 - 保持不填充
         
         // 查找并填充评论文本域
         const commentTextarea = taskContainer.querySelector(AppConfig.SELECTORS.SITE_A.COMMENT_TEXTAREA);
         if (commentTextarea) {
             if (this.commentManager && typeof this.commentManager.getRandomComment === 'function') {
                 const comment = await this.commentManager.getRandomComment();
                 commentTextarea.value = comment;
                 this.triggerInputEvents(commentTextarea);
                 Utils.log('填充到输入框', `评论已填充`);
             } else {
                 Utils.log('填充到输入框', '评论管理器不可用，无法填充评论', 'warn');
             }
         } else {
              Utils.log('填充到输入框', '未找到评论文本域', 'warn');
         }
         
         // 触发可能的LayUI或其他框架的事件更新
         this.triggerFrameworkUpdates(taskContainer);
         
         // 更新最后填充的索引
         const allUrlInputs = Array.from(document.querySelectorAll(AppConfig.SELECTORS.SITE_A.URL_INPUT));
         this.lastFilledIndex = allUrlInputs.indexOf(urlInput);
         Utils.log('填充到输入框', `更新最后填充索引为: ${this.lastFilledIndex}`);
    }

    /**
     * 触发输入框的 input 和 change 事件
     * @param {Element} inputElement - 输入框元素
     */
    triggerInputEvents(inputElement) {
        inputElement.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
        inputElement.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
        inputElement.dispatchEvent(new Event('blur', { bubbles: true, cancelable: true })); // 触发blur可能对某些验证有用
    }
    
    /**
     * 触发特定框架（如LayUI）的更新机制
     * @param {Element} container - 包含输入框的容器
     */
    triggerFrameworkUpdates(container) {
        // 针对 LayUI，可能需要调用 form.render()
        if (window.layui && window.layui.form) {
            Utils.log('框架更新', '尝试触发 LayUI 表单渲染');
            // 尝试找到包含当前容器的 layui-form 的 lay-filter
            const formFilter = container.closest('.layui-form')?.getAttribute('lay-filter');
            if (formFilter) {
                window.layui.form.render(null, formFilter);
                Utils.log('框架更新', `已触发 lay-filter="${formFilter}" 的渲染`);
            } else {
                window.layui.form.render(); // 否则尝试全局渲染
                Utils.log('框架更新', '尝试全局 LayUI 表单渲染');
            }
        } else {
             // 可以在这里添加对其他框架的支持
        }
    }

    /**
     * 更新当前页面的商品数量
     */
    updateGoodsCount() {
        const urlInputs = document.querySelectorAll(AppConfig.SELECTORS.SITE_A.URL_INPUT);
        this.goodsCount = urlInputs.length;
         Utils.log('商品计数', `当前页面商品数量: ${this.goodsCount}`);
    }
    
    /**
     * 点击添加按钮（如果存在）
     * @returns {boolean} 是否成功点击了按钮
     */
    clickAddButton() {
        const addButton = Utils.findElement(AppConfig.SELECTORS.SITE_A.ADD_TASK_BUTTON);
        if (addButton) {
            Utils.log('按钮点击', '找到并点击添加按钮');
            addButton.click();
            return true;
        } else {
            Utils.log('按钮点击', '未找到添加按钮', 'warn');
            return false;
        }
    }
    
    /**
     * 获取下一个空的URL输入框
     * 会从 lastFilledIndex 之后开始查找，实现循环查找效果
     * @returns {Element|null} - 空的URL输入框元素，或 null
     */
    getNextEmptyUrlInput() {
         Utils.log('查找输入框', `开始查找下一个空URL输入框 (从索引 ${this.lastFilledIndex + 1} 开始)`);
         const allUrlInputs = Array.from(document.querySelectorAll(AppConfig.SELECTORS.SITE_A.URL_INPUT));
         const totalInputs = allUrlInputs.length;
         
         if (totalInputs === 0) {
             Utils.log('查找输入框', '页面上没有URL输入框', 'warn');
             return null;
         }
         
         // 从上一次填充的位置之后开始查找
         for (let i = 1; i <= totalInputs; i++) {
             const currentIndex = (this.lastFilledIndex + i) % totalInputs;
             const input = allUrlInputs[currentIndex];
             // 确保输入框可见且可用
             if (input && input.offsetParent !== null && !input.disabled && !input.readOnly && input.value.trim() === '') {
                 Utils.log('查找输入框', `找到空的URL输入框，索引: ${currentIndex}`);
                 return input;
             }
         }
         
         Utils.log('查找输入框', '未找到空的URL输入框 (所有输入框都已填充)', 'warn');
         return null; // 所有输入框都已填充
    }
};

/**
 * 增强版淘宝URL解析函数
 * 支持各种格式和长度的淘宝/天猫商品链接
 * @param {string} url - 原始淘宝URL
 * @returns {object} 解析结果，包含商品ID和其他核心参数
 */
function enhancedParseTaobaoUrl(url) {
    if (!url) return null;
    
    try {
        // 尝试创建URL对象进行解析
        let parsedUrl;
        try {
            parsedUrl = new URL(url);
        } catch (e) {
            // URL可能格式不正确或过长，尝试提取核心部分
            const basicUrlMatch = url.match(/(https?:\/\/[^?]+)/i);
            if (basicUrlMatch) {
                const queryMatch = url.match(/\?(.*?)(?:&spm=|$)/i);
                const query = queryMatch ? `?${queryMatch[1]}` : '';
                parsedUrl = new URL(basicUrlMatch[1] + query);
            } else {
                throw new Error("无法解析URL基本结构");
            }
        }
        
        // 获取URL参数
        const params = {};
        // 如果过长导致URL对象解析失败，手动解析核心参数
        if (!parsedUrl.searchParams) {
            const queryPart = url.split('?')[1] || '';
            queryPart.split('&').forEach(pair => {
                const [key, value] = pair.split('=');
                if (key && value) params[key] = decodeURIComponent(value);
            });
        } else {
            // 正常解析URL参数
            parsedUrl.searchParams.forEach((value, key) => {
                params[key] = value;
            });
        }
        
        // 提取商品ID - 多种方式尝试
        let itemId = null;
        
        // 方法1: 从id参数获取
        if (params.id) {
            itemId = params.id;
        } 
        // 方法2: 从URL路径中提取ID (针对某些格式)
        else if (parsedUrl.pathname) {
            const idFromPath = parsedUrl.pathname.match(/\/(\d+)\.htm/);
            if (idFromPath && idFromPath[1]) {
                itemId = idFromPath[1];
            }
        }
        // 方法3: 针对特殊结构URL的解析
        if (!itemId) {
            const idMatch = url.match(/[?&]id=(\d+)/i);
            if (idMatch && idMatch[1]) {
                itemId = idMatch[1];
            }
        }
        // 方法4: 尝试从其他可能包含ID的参数中提取
        if (!itemId) {
            for (const key in params) {
                // 查找可能包含数字ID的参数
                if (/^i/.test(key) && /^\d+$/.test(params[key])) {
                    itemId = params[key];
                    break;
                }
            }
        }
        
        // 解析店铺ID (如果存在)
        let shopId = null;
        if (params.shop_id) {
            shopId = params.shop_id;
        } else if (params.user_id) {
            shopId = params.user_id;
        }
        
        // 判断是淘宝还是天猫
        const isTmall = url.includes('tmall.com') || 
                       url.includes('detail.tmall') || 
                       parsedUrl.hostname.includes('tmall');
        
        return {
            itemId,
            shopId,
            isTmall,
            originalUrl: url,
            // 提供一个"干净"的URL以便使用
            cleanUrl: itemId ? `https://${isTmall ? 'detail.tmall' : 'item.taobao'}.com/item.htm?id=${itemId}` : null,
            // 保存所有解析到的参数
            params
        };
    } catch (error) {
        console.error("URL解析失败:", error, url);
        
        // 最后的兜底方案：至少尝试提取商品ID
        try {
            const idMatch = url.match(/[?&]id=(\d+)/i);
            if (idMatch && idMatch[1]) {
                return {
                    itemId: idMatch[1],
                    originalUrl: url,
                    cleanUrl: `https://item.taobao.com/item.htm?id=${idMatch[1]}`,
                    isPartialParse: true
                };
            }
        } catch (e) {
            // 真的无法解析，返回null
            return null;
        }
    }
}
