/**
 * 配置文件
 * 集中管理应用的所有配置项、选择器和常量
 */

// 导出配置以便其他模块使用
window.AppConfig = {
    // AI相关配置
    AI: {
        // SiliconFlow API 配置
        API_URL: 'https://api.siliconflow.cn/v1/chat/completions',
        MODEL: 'Qwen/QwQ-32B-Preview',
        API_KEY: 'sk-rgjclhvsomldsqiazlccwuwoasboxikminjrvtlblnlsfuvb', // 重要：请替换为你的API密钥，或实现安全的密钥管理方式

        // 按钮和UI配置
        BUTTON_TEXT: 'AI分析标题',
        BUTTON_STYLE: `
            margin-left: 10px;
            margin-bottom: 5px;
            padding: 0 10px;
            height: 28px;
            line-height: 28px;
            border: 1px solid #1E9FFF;
            background-color: #1E9FFF;
            color: white;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        `,
        RESULT_CONTAINER_STYLE: `
            margin-top: 10px;
            padding: 10px;
            border: 1px dashed #ddd;
            border-radius: 3px;
            background-color: #f9f9f9;
            font-size: 12px;
            display: none;
        `,
        LOADING_TEXT: '正在分析标题并生成关键词，请稍候...',
        ERROR_TEXT: '分析失败，请重试',
        RESULT_HEADER: '🔍 AI推荐关键词：',

        // 提示词模板
        PROMPT_TEMPLATE: `分析以下淘宝商品标题，生成{count}个搜索关键词：
"{title}"

严格要求：
1. 必须生成且只生成{count}个关键词，不多不少
2. 每个关键词必须包含主体商品名称
3. 每个关键词5-10个字之间
4. 每个关键词不能重复
5. 关键词要突出商品特点
6. 必须严格按照如下JSON格式返回（不要有任何解释、序号或额外文本）：
["关键词1","关键词2","关键词3",...]`
    },

    // 选择器配置
    SELECTORS: {
        // 网站A - 商品发布页面选择器
        SITE_A: {
            // 尝试多种可能的商品名称输入框选择器
            PRODUCT_NAME_INPUT: 'input[name="item.GoodsName"], input[lay-verify="required|title"], .layui-input[placeholder*="商品名称"]',
            // 添加更多可能的标题输入框选择器
            PRODUCT_NAME_INPUT_VARIANTS: [
                'input[name="item.GoodsName"]',
                'input[lay-verify="required|title"]',
                '.layui-input[placeholder*="商品名称"]',
                'input[placeholder*="商品名称"]',
                'input[name*="GoodsName"]',
                'input[name*="goodsName"]',
                'input.layui-input[placeholder*="名称"]',
                'input[placeholder*="商品"]',
                'input[placeholder*="标题"]',
                'textarea[placeholder*="商品名称"]'
            ],
            // URL输入框选择器
            URL_INPUT: 'input[name*="Url"], input[placeholder*="链接"], input[placeholder*="URL"], input[type="text"][name*="url"]',
            // 价格输入框选择器
            PRICE_INPUT: 'input[name*="Price"], input[placeholder*="价格"], input[type="text"][name*="price"]',
            // 评论文本域选择器
            COMMENT_TEXTAREA: 'textarea[name*="Comment"], textarea[placeholder*="评论"], textarea[name*="comment"]',
            // 添加任务按钮选择器
            ADD_TASK_BUTTON: 'button[onclick*="addTask"], .add-task-btn, button:contains("添加"), button:contains("新增")',
            // 关键词输入框选择器
            KEYWORD_INPUT: 'input[lay-verify="req"][placeholder*="搜索关键词"], input.layui-input[placeholder*="搜索关键词"], .task_keyword_box input[type="text"], div.layui-input-inline input[type="text"][name*="keyword"], input[name="item.SearchKeywordType"]',
            TASK_COUNT_CONTAINER: 'li[name^="lstPicTask"]:last-child',
            // 任务容器
            TASK_CONTAINER: 'li[name^="lstPicTask"]'
        },

        // 淘宝相关选择器
        TAOBAO: {
            REVIEW_CONTAINER: '.rate-list, .tb-revbd',
            IMAGE_CONTAINER: '.tm-m-photos, .review-details-pictures',
            PRICE_ELEMENT: ['.tb-rmb-num', '#J_StrPrice', '.tm-price']
        }
    },

    // 全局样式配置
    STYLES: {
        // 控制面板样式
        CONTROL_PANEL: `
            position: fixed;
            top: 100px;
            right: 20px;
            width: 220px;
            padding: 15px;
            background: white;
            border: 2px solid #1E9FFF;
            border-radius: 5px;
            z-index: 9999999;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        `,
        // 悬浮按钮容器样式
        FLOATING_BUTTON_CONTAINER: `
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 9999999;
        `,
        // 悬浮按钮样式
        FLOATING_BUTTON: `
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: #1E9FFF;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `,
        // 提示框样式
        TOOLTIP: `
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 10000000;
            pointer-events: none;
        `,
        // 复选框样式
        CHECKBOX: `
            width: 18px;
            height: 18px;
            margin-right: 5px;
            vertical-align: middle;
            cursor: pointer;
        `,
        // 结果容器样式
        RESULT_CONTAINER: `
            position: fixed;
            top: 150px;
            right: 50px;
            width: 300px;
            padding: 15px;
            background: white;
            border: 2px solid #1E9FFF;
            border-radius: 5px;
            z-index: 9999999;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            max-height: 400px;
            overflow-y: auto;
        `
    },

    // 调试模式
    DEBUG: true,

    // 目标网站配置
    SITES: {
        TAOBAO: {
            HOST: ['item.taobao.com', 'detail.tmall.com'],
            URL_PATTERN: /\/\/(?:item\.taobao|detail\.tmall)\.com/
        },
        SITE_A: {
            HOST: ['ses.zzds888.com', 'order.sanmato.com'],
            URL_PATTERN: /\/\/(ses\.zzds888\.com|order\.sanmato\.com)/
        }
    }
};

// 为了兼容性，也提供一个AI_CONFIG变量
window.AI_CONFIG = window.AppConfig.AI;
