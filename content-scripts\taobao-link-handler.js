// 淘宝链接处理器
window.TaobaoLinkHandler = class TaobaoLinkHandler {
    constructor() {
        this.processedLinks = new Set();
        Utils.log('淘宝链接处理器', '初始化');
        this.initObserver();
    }

    // 初始化 MutationObserver 监听链接变化
    initObserver() {
        const observer = new MutationObserver((mutations) => {
            // 防抖处理
            clearTimeout(this._observerDebounce);
            this._observerDebounce = setTimeout(() => {
                this.processNewLinks();
            }, AppConfig.DELAYS.LINK_PROCESS_DEBOUNCE || 500); // 延迟处理
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['href'] // 仅关注 href 变化
        });

        // 初始执行一次
        this.processNewLinks();
    }

    // 处理页面上新增的或变化的链接
    processNewLinks() {
        // 查找所有符合条件的淘宝链接
        const links = document.querySelectorAll('a[href*="item.taobao.com/item.htm?id="], a[href*="detail.tmall.com/item.htm?id="]');

        links.forEach(link => {
            const href = link.href;
            // 如果链接已处理或已存在按钮，则跳过
            if (this.processedLinks.has(href) || link.querySelector('.quick-access-button-taobao')) {
                return;
            }

            // 检查是否是合适的插入位置（例如，在一个商品项内部）
            const productContainer = link.closest(AppConfig.SELECTORS.TAOBAO.LINK_CONTAINER);
            if (productContainer) {
                this.addQuickAccessButton(link, productContainer);
                this.processedLinks.add(href);
            }
        });
    }

    // 添加快速访问按钮
    addQuickAccessButton(link, container) {
         // 确保按钮只添加一次
        if (container.querySelector('.quick-access-button-taobao')) return;

        const button = Utils.createElement('button', {
            text: '快速访问',
            className: 'quick-access-button-taobao',
            style: AppConfig.STYLES.QUICK_ACCESS_BUTTON
        });

        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation(); // 阻止链接默认跳转和事件冒泡
            Utils.log('淘宝链接处理器', '快速访问按钮点击', link.href);
            // 发送消息到 Background，请求在后台打开链接
            chrome.runtime.sendMessage({ type: 'openLinkInBackground', url: link.href }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('发送消息到Background失败:', chrome.runtime.lastError.message);
                    Utils.log('淘宝链接处理器', `发送打开链接请求失败: ${chrome.runtime.lastError.message}`, 'error');
                    alert('打开链接失败，请检查扩展程序权限或后台脚本是否运行正常。');
                } else if (response && response.success) {
                    Utils.log('淘宝链接处理器', '后台打开链接成功', link.href);
                    // 可选：给用户一个视觉反馈
                    button.textContent = '已打开';
                    button.disabled = true;
                    setTimeout(() => {
                        button.textContent = '快速访问';
                        button.disabled = false;
                    }, 2000);
                } else {
                    Utils.log('淘宝链接处理器', `后台打开链接失败: ${response?.error || '未知错误'}`, 'error');
                    alert(`无法在后台打开链接: ${response?.error || '请重试'}`);
                }
            });
        });

        // 将按钮添加到合适的位置，例如链接旁边或容器末尾
        // 尝试添加到链接后面
        if (link.nextSibling) {
             link.parentNode.insertBefore(button, link.nextSibling);
        } else {
             link.parentNode.appendChild(button);
        }
        
        // 如果父容器空间不足，尝试添加到外层容器
        // if (!this.isInlineElement(link.parentNode)) {
        //     link.parentNode.appendChild(button);
        // } else {
        //     container.appendChild(button); // 添加到商品容器末尾
        // }
    }
    
    // 辅助函数判断元素是否可能是内联显示的
    // isInlineElement(element) {
    //     const display = window.getComputedStyle(element).display;
    //     return display.startsWith('inline');
    // }
} 