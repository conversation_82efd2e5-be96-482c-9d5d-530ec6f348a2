(function() {
    'use strict';

    /**
     * 淘宝晒图转移工具
     *
     * 功能说明：
     * 1. 在淘宝评价弹窗中添加勾选框，用于选择评价图片
     * 2. 提供全选功能和一键转移功能
     * 3. 支持将评价图片转移到目标网站
     * 4. 提供复制淘宝商品链接和价格功能
     *
     * 使用说明：
     * 1. 在淘宝商品详情页点击评价，打开评价弹窗
     * 2. 勾选需要转移的评价图片
     * 3. 点击"一键转移"按钮，将图片转移到目标网站
     *
     * 版本历史：
     * v1.0.0 - 初始版本
     * v1.1.0 - 适配淘宝新版页面结构
     * v1.2.0 - 优化界面布局，添加错误处理和日志
     *
     * 最后更新：2025-03-31
     */

    // 配置中心 - 集中管理所有选择器和常量
    const CONFIG = {
        // 淘宝评价弹窗选择器
        selectors: {
            // 弹窗容器
            popupContainer: [
                '.content--ew3Y4lVg',
                '.E7gD8doUq1--content--e320bf32',
                '.QJEEHAN8H5--content--e320bf32',
                '[class*="--content--"]'
            ],
            // 评价容器
            reviewsContainer: [
                '.comments--vOMSBfi2',
                '.E7gD8doUq1--Comments--cc29f3ef',
                '.QJEEHAN8H5--comments--_001028c',
                '[class*="--comments--"]',
                '[class*="--Comments--"]'
            ],
            // 标题容器
            titleWrapper: [
                '.E7gD8doUq1--titleWrapper--_698f792',
                '.QJEEHAN8H5--titleWrapper--_698f792',
                '[class*="--titleWrapper--"]'
            ],
            // 单条评价
            reviewItem: [
                '.E7gD8doUq1--Comment--_0b4e753',
                '.QJEEHAN8H5--Comment--_0b4e753',
                '[class*="--Comment--"]'
            ],
            // 图片区域
            albumArea: [
                '.E7gD8doUq1--album--fc5a8d2b',
                '.QJEEHAN8H5--album--fc5a8d2b',
                '[class*="--album--"]'
            ],
            // 标签项
            tagItems: [
                '.E7gD8doUq1--tagItem--_52372dc',
                '.E7gD8doUq1--sortItem--_8fe9e39',
                '.QJEEHAN8H5--tagItem--_52372dc',
                '.QJEEHAN8H5--sortItem--_8fe9e39',
                '[class*="--tagItem--"]',
                '[class*="--sortItem--"]',
                '[class*="--tab"]'
            ],
            // 价格元素
            priceElement: [
                '.E7gD8doUq1--text--_4c1ce7d',  // 新版价格
                '.QJEEHAN8H5--text--_4c1ce7d',  // 新版价格(新前缀)
                '.text--fZ9NUhyQ',              // 旧版价格
                '[class*="--text--"][class*="ce7d"]',  // 备选价格1
                '[class*="Price"] [class*="text"]'     // 备选价格2
            ]
        },
        // 自定义类名
        classNames: {
            checkboxWrapper: 'review-checkbox-wrapper',
            checkbox: 'review-checkbox',
            controlPanel: 'review-control-panel'
        },
        // 延时配置（毫秒）
        delays: {
            tabSwitch: 800,
            imageLoad: 500,
            retryReview: 1000,
            retryAlbum: 500,
            loadTimeout: 5000
        },
        // 样式配置
        styles: {
            // 控制面板样式
            controlPanel: `
                display: inline-flex;
                align-items: center;
                margin-left: 15px;
                gap: 8px;
                vertical-align: middle;
            `,
            // 勾选框包装器样式
            checkboxWrapper: `
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 9999;
                background: rgba(255,255,255,0.9);
                padding: 4px 6px;
                border-radius: 4px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                gap: 5px;
            `,
            // 勾选框样式
            checkbox: `
                width: 18px;
                height: 18px;
                margin: 0;
                cursor: pointer;
            `,
            // 复制按钮样式
            copyButton: `
                position: fixed;
                top: 20px;
                left: 20px;
                background: #000000;
                color: white;
                width: 40px;
                height: 40px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 10000;
                font-size: 20px;
                transition: transform 0.2s;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            `
        },
        // 目标网站配置
        targetSite: {
            url: 'https://ses.zzds888.com:1655/Task/AddTask'  // 默认使用第一个域名
        }
    };

    /**
     * 工具函数库 - 常用的辅助功能
     */
    const Utils = {
        /**
         * 查找元素 - 支持多个选择器，返回第一个匹配的元素
         * @param {string[] | string} selectors - 选择器或选择器数组
         * @param {Element} [parent=document] - 父元素，默认为document
         * @returns {Element|null} - 返回匹配的元素或null
         */
        findElement(selectors, parent = document) {
            if (!selectors) return null;

            const selectorArray = Array.isArray(selectors) ? selectors : [selectors];

            for (const selector of selectorArray) {
                try {
                    const element = parent.querySelector(selector);
                    if (element) return element;
                } catch (e) {
                    console.error('选择器错误:', selector, e);
                }
            }

            return null;
        },

        /**
         * 查找所有元素 - 支持选择器，返回所有匹配的元素
         * @param {string} selector - 选择器
         * @param {Element} [parent=document] - 父元素，默认为document
         * @returns {NodeListOf<Element>} - 返回匹配的元素列表
         */
        findAllElements(selector, parent = document) {
            if (!selector) return [];

            try {
                return parent.querySelectorAll(selector);
            } catch (e) {
                console.error('选择器错误:', selector, e);
                return [];
            }
        },

        /**
         * 创建元素并设置属性和样式
         * @param {string} tag - 标签名
         * @param {Object} [options={}] - 配置选项
         * @param {string} [options.id] - 元素ID
         * @param {string|string[]} [options.className] - 类名或类名数组
         * @param {string} [options.html] - 内部HTML
         * @param {string} [options.text] - 文本内容
         * @param {string} [options.css] - CSS样式文本
         * @param {Object} [options.attrs] - 属性对象
         * @param {Object} [options.events] - 事件处理函数对象
         * @returns {Element} - 创建的元素
         */
        createElement(tag, options = {}) {
            const element = document.createElement(tag);

            if (options.id) element.id = options.id;

            if (options.className) {
                const classNames = Array.isArray(options.className)
                    ? options.className
                    : [options.className];
                element.classList.add(...classNames);
            }

            if (options.html) element.innerHTML = options.html;
            if (options.text) element.textContent = options.text;
            if (options.css) element.style.cssText = options.css;

            if (options.attrs) {
                Object.entries(options.attrs).forEach(([key, value]) => {
                    element.setAttribute(key, value);
                });
            }

            if (options.events) {
                Object.entries(options.events).forEach(([event, handler]) => {
                    element.addEventListener(event, handler);
                });
            }

            return element;
        },

        /**
         * 记录日志，增加自定义前缀
         * @param {string} level - 日志级别: log, warn, error, info
         * @param {string} message - 日志消息
         * @param {...any} args - 额外参数
         */
        log(level, message, ...args) {
            const prefix = '[淘宝晒图工具]';
            const method = console[level] || console.log;
            method(`${prefix} ${message}`, ...args);
        }
    };

    // 图片收集器
    class ImageCollector {
        constructor() {
            this.selectedGroups = new Map(); // 存储选中的图片组
            this.controlPanelAdded = false;  // 标记控制面板是否已添加
            this.isProcessing = false; // 添加处理状态标记
        }

        // 初始化界面
        initializeInterface() {
            Utils.log('log', '开始初始化界面');
            this.addCheckboxesToReviews();
        }

        // 添加控制面板到评价弹窗
        addControlPanelToPopup(popupContainer) {
            if (this.controlPanelAdded) {
                // 检查控制面板是否还存在
                const existingPanel = document.getElementById(CONFIG.classNames.controlPanel);
                if (!existingPanel) {
                    this.controlPanelAdded = false; // 如果面板不存在，重置标记
                } else {
                    return; // 如果面板存在，直接返回
                }
            }

            // 查找评价容器 - 优先使用更精确的选择器
            let reviewsContainer = null;

            // 1. 尝试找到评论容器
            reviewsContainer = Utils.findElement(CONFIG.selectors.reviewsContainer, popupContainer);

            // 2. 如果找不到，尝试通过其他方式查找
            if (!reviewsContainer) {
                // 查找包含评论内容的容器
                const comments = popupContainer.querySelectorAll('[class*="--Comment--"]');
                if (comments.length > 0) {
                    reviewsContainer = comments[0].parentElement;
                    Utils.log('log', '通过评论项的父元素找到评论容器');
                }
            }

            // 3. 还是找不到，尝试找有图片的评论区域
            if (!reviewsContainer) {
                const imgContainers = Array.from(popupContainer.querySelectorAll('div')).filter(div => {
                    const imgs = div.querySelectorAll('img[src*="img.alicdn.com"]');
                    return imgs.length > 0;
                });

                if (imgContainers.length > 0) {
                    reviewsContainer = imgContainers[0].closest('div[class*="comments"], div[class*="Comments"], div[class*="content"]');
                    Utils.log('log', '通过图片找到评论容器');
                }
            }

            if (!reviewsContainer) {
                Utils.log('warn', '未找到评价容器');
                return;
            }

            // 检查是否已经存在控制面板
            if (reviewsContainer.querySelector(`#${CONFIG.classNames.controlPanel}`)) {
                this.controlPanelAdded = true;
                return;
            }

            // 创建控制面板
            const panel = this.createControlPanel();

            // 尝试查找适合放置控制面板的位置

            // 1. 特别处理：如果是侧边弹出的弹窗，尝试找到其顶部工具栏
            const isDrawer = popupContainer.className.includes('drawer') ||
                          popupContainer.className.includes('Drawer') ||
                          window.getComputedStyle(popupContainer).position === 'fixed';

            if (isDrawer) {
                Utils.log('log', '检测到侧边弹窗，尝试找到顶部工具栏');

                // 先尝试查找"用户评价"标题
                const userReviewTitleSelector = '.QJEEHAN8H5--tabDetailItemTitle--_66fb07e, [class*="--tabDetailItemTitle--"]';

                // 首先在弹窗内部查找
                let titleElement = popupContainer.querySelector(userReviewTitleSelector);

                // 如果弹窗内部找不到，扩大范围到整个文档
                if (!titleElement) {
                    titleElement = document.querySelector(userReviewTitleSelector);
                }

                // 如果找不到特定类名，尝试查找包含"用户评价"文本的span元素
                if (!titleElement) {
                    // 先在弹窗内部查找
                    const popupSpans = Array.from(popupContainer.querySelectorAll('span'));
                    titleElement = popupSpans.find(span =>
                        span.textContent &&
                        span.textContent.trim() === '用户评价' &&
                        window.getComputedStyle(span).display !== 'none'
                    );

                    // 如果弹窗内部找不到，扩大范围到整个文档
                    if (!titleElement) {
                        const allSpans = Array.from(document.querySelectorAll('span'));
                        titleElement = allSpans.find(span =>
                            span.textContent &&
                            span.textContent.trim() === '用户评价' &&
                            window.getComputedStyle(span).display !== 'none'
                        );
                    }

                    if (titleElement) {
                        Utils.log('log', '通过文本内容找到"用户评价"标题');
                    }
                }

                // 如果找到了用户评价标题，将控制面板放在它旁边
                if (titleElement && titleElement.isConnected) {
                    Utils.log('log', '找到"用户评价"标题，将控制面板放在旁边');

                    // 修改panel样式使其适合内联显示
                    panel.style.display = 'inline-flex';
                    panel.style.marginLeft = '15px';
                    panel.style.verticalAlign = 'middle';
                    panel.style.position = 'relative'; // 确保不是绝对定位

                    // 将面板插入到标题旁边
                    const titleParent = titleElement.parentNode;
                    if (titleParent) {
                        if (titleElement.nextSibling) {
                            titleParent.insertBefore(panel, titleElement.nextSibling);
                        } else {
                            titleParent.appendChild(panel);
                        }

                        this.controlPanelAdded = true;

                        // 初始化事件监听
                        this.initializeEventListeners();
                        return;
                    }
                }

                // 如果找不到用户评价标题，回退到查找弹窗头部等方法
                // 查找可能的侧边弹窗工具栏或头部
                const drawerHeader = popupContainer.querySelector(
                    'header, [class*="header"], [class*="Header"], [class*="title"], [class*="Title"], [class*="toolbar"], [class*="Toolbar"]'
                );

                // 查找顶部的操作区域
                const topControls = popupContainer.querySelector(
                    '[class*="controls"], [class*="Controls"], [class*="actions"], [class*="Actions"], [class*="tabs"], [class*="Tabs"]'
                );

                // 查找顶部"全部"标签或者该标签所在的容器
                const allTab = Array.from(popupContainer.querySelectorAll('div, span, li')).find(
                    el => el.textContent.trim() === '全部' || el.textContent.trim() === '全部评价'
                );

                const tabContainer = allTab ? allTab.parentElement : null;

                // 如果找到侧边弹窗头部，尝试添加控制面板
                if (drawerHeader) {
                    Utils.log('log', '找到侧边弹窗头部，添加控制面板');

                    // 创建一个容器来包装控制面板，使其更好地融入侧边弹窗布局
                    const panelContainer = Utils.createElement('div', {
                        css: `
                            position: absolute;
                            top: 50%;
                            right: 10px;
                            transform: translateY(-50%);
                            display: flex;
                            align-items: center;
                            z-index: 1000;
                        `
                    });

                    panelContainer.appendChild(panel);
                    drawerHeader.appendChild(panelContainer);
                    this.controlPanelAdded = true;

                    // 初始化事件监听
                    this.initializeEventListeners();
                    return;
                }

                // 如果找到顶部操作区域，添加到其中
                if (topControls) {
                    Utils.log('log', '找到顶部操作区域，添加控制面板');
                    topControls.appendChild(panel);
                    this.controlPanelAdded = true;

                    // 初始化事件监听
                    this.initializeEventListeners();
                    return;
                }

                // 如果找到标签容器，添加到标签容器中
                if (tabContainer) {
                    Utils.log('log', '找到标签容器，添加控制面板');

                    // 如果是列表，创建一个列表项
                    if (tabContainer.tagName === 'UL' || tabContainer.tagName === 'OL') {
                        const li = Utils.createElement('li', {
                            css: `
                                display: inline-block;
                                margin-left: 10px;
                                vertical-align: middle;
                            `
                        });
                        li.appendChild(panel);
                        tabContainer.appendChild(li);
                    } else {
                        tabContainer.appendChild(panel);
                    }

                    this.controlPanelAdded = true;

                    // 初始化事件监听
                    this.initializeEventListeners();
                    return;
                }

                // 如果上面的方法都失败了，尝试找模板标签位置
                const modelLabels = Array.from(popupContainer.querySelectorAll('label, div, span')).filter(
                    el => el.textContent.includes('模板') || el.textContent.includes('选择')
                );

                if (modelLabels.length > 0) {
                    Utils.log('log', '找到模板标签，添加控制面板到其旁边');
                    const modelLabel = modelLabels[0];
                    const labelParent = modelLabel.parentElement;

                    if (labelParent) {
                        labelParent.appendChild(panel);
                        this.controlPanelAdded = true;

                        // 初始化事件监听
                        this.initializeEventListeners();
                        return;
                    }
                }
            }

            // 2. 如果不是侧边弹窗或上述方法失败，回退到其他方法
            // 尝试找到标题容器 (原方法)
            let titleWrapper = Utils.findElement(CONFIG.selectors.titleWrapper, reviewsContainer);

            // 如果找不到标题容器，尝试找页面顶部的Tab栏或导航栏
            if (!titleWrapper) {
                Utils.log('log', '未找到标题容器，尝试找顶部导航栏');

                // 尝试查找评价弹窗中的导航栏、过滤栏或选项卡
                const possibleContainers = [
                    // 查找可能的导航栏或工具栏
                    reviewsContainer.querySelector('.E7gD8doUq1--filter--_c32c15f'),
                    reviewsContainer.querySelector('.QJEEHAN8H5--filter--_c32c15f'),
                    reviewsContainer.querySelector('[class*="--filter--"]'),

                    // 新版淘宝评论选项卡容器
                    reviewsContainer.querySelector('[class*="--tabs--"]'),

                    // Tab栏
                    reviewsContainer.querySelector('[role="tablist"]'),

                    // 尝试通过"全部"选项卡找到其父容器
                    reviewsContainer.querySelector('li:first-child'),

                    // 页面顶部的操作区域
                    popupContainer.querySelector('header'),

                    // 查找第一个评论前面的元素
                    reviewsContainer.querySelector('[class*="--Comment--"]') ?
                      reviewsContainer.querySelector('[class*="--Comment--"]').previousElementSibling : null
                ];

                // 找到第一个非空的容器
                titleWrapper = possibleContainers.find(el => el !== null);
            }

            // 3. 还是找不到，尝试插入到第一个评论之前
            if (!titleWrapper) {
                Utils.log('log', '未找到合适的容器，尝试找第一个评论');
                const firstComment = reviewsContainer.querySelector('[class*="--Comment--"]');
                if (firstComment) {
                    Utils.log('log', '找到第一个评论，将控制面板插入到评论前');
                    firstComment.parentNode.insertBefore(panel, firstComment);
                this.controlPanelAdded = true;

                // 初始化事件监听
                this.initializeEventListeners();

                // 给面板添加后立即处理评价列表
                this.processNewReviews(reviewsContainer);
                return;
                }
            }

            // 4. 再尝试一种方法 - 查找全选功能所在的区域
            if (!titleWrapper) {
                Utils.log('log', '尝试查找全选功能所在区域');

                // 尝试查找页面中已存在的全选功能
                const existingSelectAll = Array.from(reviewsContainer.querySelectorAll('label')).find(
                    label => label.textContent.includes('全选') || label.textContent.includes('选择')
                );

                if (existingSelectAll) {
                    Utils.log('log', '找到已存在的全选功能，使用其父元素');
                    titleWrapper = existingSelectAll.parentElement;
                }
            }

            // 5. 最后尝试 - 查找页面上的按钮或操作区
            if (!titleWrapper) {
                Utils.log('log', '尝试查找页面上的按钮区域');

                // 查找页面上任何可能的按钮或操作区域
                const buttons = reviewsContainer.querySelectorAll('button');
                if (buttons.length > 0) {
                    const lastButton = buttons[buttons.length - 1];
                    titleWrapper = lastButton.parentElement;
                }
            }

            // 6. 如果找到了合适的位置，添加控制面板
            if (titleWrapper) {
                Utils.log('log', '找到合适位置，添加控制面板');

                // 检查是否需要作为第一个子元素添加
                if (titleWrapper.tagName === 'UL' || titleWrapper.tagName === 'OL') {
                    // 创建一个Li元素作为容器
                    const liContainer = Utils.createElement('li', {
                        css: 'display: inline-block; margin-left: 10px;'
                    });
                    liContainer.appendChild(panel);
                    titleWrapper.appendChild(liContainer);
                } else {
            titleWrapper.appendChild(panel);
                }

            this.controlPanelAdded = true;
            } else {
                // 7. 实在找不到，将面板添加到评价容器的顶部
                Utils.log('warn', '未找到任何合适位置，添加到评价容器顶部');
                reviewsContainer.insertBefore(panel, reviewsContainer.firstChild);
                this.controlPanelAdded = true;
            }

            // 初始化事件监听
            this.initializeEventListeners();

            // 给面板添加后立即处理评价列表
            this.processNewReviews(reviewsContainer);
        }

        // 创建控制面板 - 保留原方法但不使用，保证向后兼容
        createControlPanel() {
            const panel = document.createElement('div');
            panel.id = CONFIG.classNames.controlPanel;
            panel.style.cssText = CONFIG.styles.controlPanel;

            panel.innerHTML = `
                <label style="margin-right: 5px; white-space: nowrap;">
                    <input type="checkbox" id="selectAllReviews" style="margin-right: 3px;"> 全选
                </label>
                <button id="autoTransfer" style="
                    padding: 3px 8px;
                    background: #1E9FFF;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    white-space: nowrap;
                ">
                    一键转移(<span id="selectedCount">0</span>)
                </button>
                <button id="stopTransfer" style="
                    padding: 3px 8px;
                    background: #FF4D4F;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    display: none;
                    white-space: nowrap;
                ">
                    终止转移
                </button>
            `;

            return panel;
        }

        // 初始化事件监听
        initializeEventListeners() {
            const panel = document.getElementById(CONFIG.classNames.controlPanel);
            if (!panel) return;

            // 全选按钮
            const selectAllCheckbox = panel.querySelector('#selectAllReviews');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', (e) => {
                    const checkboxes = document.querySelectorAll(`.${CONFIG.classNames.checkbox}`);
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = e.target.checked;
                        this.handleCheckboxChange(checkbox);
                    });
                });
            }

            // 一键转移按钮
            const transferButton = panel.querySelector('#autoTransfer');
            if (transferButton) {
                transferButton.addEventListener('click', () => {
                    this.handleTransfer();
                });
            }

            // 终止转移按钮
            const stopButton = panel.querySelector('#stopTransfer');
            if (stopButton) {
                stopButton.addEventListener('click', () => {
                    chrome.runtime.sendMessage({ type: 'stopProcessing' });
                    this.updateButtonState(false);
                });
            }
        }

        // 添加复选框到评价
        addCheckboxesToReviews() {
            Utils.log('log', '开始监听DOM变化');

            // 先尝试处理已有的评价弹窗
            this.checkExistingPopup();

            const observer = new MutationObserver((mutations) => {
                // 使用防抖处理，避免频繁触发
                clearTimeout(this._debounceTimer);
                this._debounceTimer = setTimeout(() => {
                    // 查找淘宝评价弹窗容器 - 特别注意侧边弹出的对话框
                    this.checkExistingPopup();
                }, 300); // 300ms防抖
            });

            // 监听DOM变化，特别关注body及其子元素的变化
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true // 启用属性变化监听，以便捕获弹窗的显示/隐藏状态变化
            });

            // 添加标签切换的事件监听
            document.addEventListener('click', (e) => {
                // 检测评论相关按钮点击
                const reviewButton = e.target.closest('[class*="review"], [class*="comment"], .rate-list, .tb-rate-popup, [class*="feedback"]');
                if (reviewButton) {
                    Utils.log('log', '检测到评论按钮点击，等待弹窗出现');
                    // 给弹窗一点时间加载
                    setTimeout(() => this.checkExistingPopup(), 500);
                }

                // 标签切换点击
                const tabElement = e.target.closest(CONFIG.selectors.tagItems[0]) ||
                                 e.target.closest(CONFIG.selectors.tagItems[1]) ||
                                 e.target.closest(CONFIG.selectors.tagItems[2]) ||
                                 e.target.closest('[class*="tab"], [role="tab"]');

                if (tabElement) {
                    Utils.log('log', '检测到标签切换点击');
                    // 给一点时间让新内容加载
                    setTimeout(() => {
                        // 重置控制面板状态
                        this.controlPanelAdded = false;
                        const existingPanel = document.getElementById(CONFIG.classNames.controlPanel);
                        if (existingPanel) {
                            existingPanel.remove();
                        }

                        this.selectedGroups.clear(); // 清空已选择的组
                        this.checkExistingPopup();
                    }, CONFIG.delays.tabSwitch); // 使用配置的等待时间
                }
            });
        }

        // 检查是否存在评价弹窗，如果存在则处理
        checkExistingPopup() {
            // 1. 尝试查找侧边弹出的对话框容器 - 这是最外层容器
            const sideDrawers = Array.from(document.querySelectorAll(
                'div[class*="drawer"], div[style*="z-index"][style*="fixed"], div[class*="leftDrawer"], [class*="content-"][style*="display"]'
            )).filter(el => {
                // 过滤出可见的高z-index元素，可能是弹窗
                const style = window.getComputedStyle(el);
                const isVisible = style.display !== 'none' && style.visibility !== 'hidden';
                const hasHighZIndex = parseInt(style.zIndex) > 100;
                return isVisible && hasHighZIndex;
            });

            Utils.log('log', `找到可能的侧边弹窗数量: ${sideDrawers.length}`);

            // 2. 遍历每个可能的弹窗容器
            for (const drawer of sideDrawers) {
                // 检查是否包含评论内容
                const hasComments =
                    drawer.querySelector('[class*="--Comment--"]') ||
                    drawer.querySelector('[class*="comment"]') ||
                    drawer.querySelector('[class*="review"]') ||
                    drawer.querySelector('[class*="feedback"]') ||
                    drawer.querySelector('img[src*="img.alicdn.com"]'); // 评论中常有淘宝图片

                if (hasComments) {
                    Utils.log('log', '找到包含评论的侧边弹窗');

                    // 直接在整个弹窗容器中查找评价容器
                    let reviewsContainer = Utils.findElement(CONFIG.selectors.reviewsContainer, drawer);

                    // 如果找不到，尝试查找任何可能包含评论的容器
                    if (!reviewsContainer) {
                        reviewsContainer = drawer.querySelector('[class*="comments"], [class*="Comments"], [class*="reviews"], .tb-rev-bd, .rate-list');
                    }

                    // 如果仍找不到，但有评论项，则使用父容器
                    if (!reviewsContainer && drawer.querySelector('[class*="--Comment--"]')) {
                        reviewsContainer = drawer.querySelector('[class*="--Comment--"]').parentElement;
                    }

                            if (reviewsContainer) {
                        Utils.log('log', '找到评论容器，添加控制面板');
                        this.addControlPanelToPopup(drawer);
                                this.processNewReviews(reviewsContainer);

                        // 如果成功找到并处理，可以跳出循环
                        break;
                    }
                }
            }

            // 3. 如果上面的方法都失败了，回退到原来的方法
            if (!this.controlPanelAdded) {
                const existingPopupContainer = Utils.findElement(CONFIG.selectors.popupContainer);
                if (existingPopupContainer) {
                    Utils.log('log', '找到已存在的评价弹窗 (使用传统方法)');
                    this.addControlPanelToPopup(existingPopupContainer);

                    const reviewsContainer = Utils.findElement(CONFIG.selectors.reviewsContainer, existingPopupContainer);
                    if (reviewsContainer) {
                        this.processNewReviews(reviewsContainer);
                    }
                }
            }
        }

        // 处理新的评价
        processNewReviews(container) {
            // 查找评价项
            const reviewItems = Utils.findAllElements(CONFIG.selectors.reviewItem, container);

            Utils.log('log', '找到评价数量:', reviewItems.length);

            if (reviewItems.length === 0) {
                // 尝试延迟处理，可能DOM还没完全加载
                setTimeout(() => {
                    const retryItems = Utils.findAllElements(CONFIG.selectors.reviewItem, container);
                    Utils.log('log', '延迟后找到评价数量:', retryItems.length);
                    if (retryItems.length > 0) {
                        this.processReviewItems(retryItems);
                    }
                }, CONFIG.delays.retryReview);
                return;
            }

            this.processReviewItems(reviewItems);
        }

        // 处理评价项集合
        processReviewItems(reviewItems) {
            reviewItems.forEach((review, index) => {
                if (review.querySelector(`.${CONFIG.classNames.checkboxWrapper}`)) {
                    Utils.log('log', '评价已有复选框，跳过');
                    return; // 已添加复选框
                }

                // 查找图片区域
                const albumArea = Utils.findElement(CONFIG.selectors.albumArea, review);
                if (!albumArea) {
                    Utils.log('warn', '未找到图片区域', review);

                    // 可能图片区域加载延迟，延后再试一次
                    setTimeout(() => {
                        const retryAlbumArea = Utils.findElement(CONFIG.selectors.albumArea, review);
                        if (retryAlbumArea && !review.querySelector(`.${CONFIG.classNames.checkboxWrapper}`)) {
                            this.addCheckboxToAlbum(retryAlbumArea, review, index);
                        }
                    }, CONFIG.delays.retryAlbum);

                    return; // 没有图片区域
                }

                Utils.log('log', '为评价添加复选框', index);
                this.addCheckboxToAlbum(albumArea, review, index);
            });
        }

        // 给相册区域添加复选框
        addCheckboxToAlbum(albumArea, review, index) {
            // 检查是否有图片
            const images = albumArea.querySelectorAll('img');
            if (images.length === 0) {
                Utils.log('log', '相册区域没有图片，等待图片加载');

                // 监听图片加载
                const observer = new MutationObserver((mutations, obs) => {
                    const updatedImages = albumArea.querySelectorAll('img');
                    if (updatedImages.length > 0) {
                        Utils.log('log', '图片已加载，添加复选框');
                        this.createCheckboxWrapper(albumArea, review, index);
                        obs.disconnect();
                    }
                });

                observer.observe(albumArea, { childList: true, subtree: true });

                // 设置超时，避免无限等待
                setTimeout(() => {
                    observer.disconnect();
                    this.createCheckboxWrapper(albumArea, review, index);
                }, CONFIG.delays.loadTimeout);

                return;
            }

            // 有图片，直接添加复选框
            this.createCheckboxWrapper(albumArea, review, index);
        }

        // 创建复选框包装器
        createCheckboxWrapper(albumArea, review, index) {
            // 避免重复添加
            if (albumArea.querySelector(`.${CONFIG.classNames.checkboxWrapper}`)) {
                return;
            }

            // 创建复选框包装器
            const wrapper = Utils.createElement('div', {
                className: CONFIG.classNames.checkboxWrapper,
                css: CONFIG.styles.checkboxWrapper
            });

            // 创建复选框
            const checkbox = Utils.createElement('input', {
                attrs: {
                    type: 'checkbox',
                    'data-review-index': index
                },
                className: CONFIG.classNames.checkbox,
                css: CONFIG.styles.checkbox,
                events: {
                    change: () => this.handleCheckboxChange(checkbox)
                }
            });

            // 创建复制按钮
            const copyButton = Utils.createElement('button', {
                text: '复制',
                css: `
                    padding: 1px 4px;
                    background: #1E9FFF;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                    line-height: 1.2;
                `,
                attrs: {
                    'data-review-index': index,
                    'title': '复制这组图片到剪贴板'
                },
                events: {
                    click: (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.handleCopyImages(albumArea);
                    }
                }
            });

            // 创建下载按钮
            const downloadButton = Utils.createElement('button', {
                text: '下载',
                css: `
                    padding: 1px 4px;
                    background: #009688;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                    line-height: 1.2;
                `,
                attrs: {
                    'data-review-index': index,
                    'title': '下载这组图片'
                },
                events: {
                    click: (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.handleDownloadImages(albumArea);
                    }
                }
            });

            wrapper.appendChild(checkbox);
            wrapper.appendChild(copyButton);
            wrapper.appendChild(downloadButton);

            // 强制设置相对定位 - 这是关键修复
            albumArea.style.position = 'relative';

            // 添加复选框
            albumArea.appendChild(wrapper);
            Utils.log('log', '复选框和按钮添加完成');
        }

        // 处理复选框变化
        handleCheckboxChange(checkbox) {
            // 查找评价项
            const review = checkbox.closest('[class*="--Comment--"]');

            if (!review) {
                Utils.log('warn', '无法找到评价项元素');
                return;
            }

            // 查找图片区域
            const albumArea = review.querySelector('[class*="--album--"]');

            if (!albumArea) {
                Utils.log('warn', '无法找到图片区域元素');
                return;
            }

            const reviewIndex = checkbox.dataset.reviewIndex;

            if (checkbox.checked) {
                // 确保该评价可见
                review.scrollIntoView({ behavior: 'smooth', block: 'center' });
                // 给一点时间让图片加载
                setTimeout(() => {
                    // 查找图片元素
                    const images = albumArea.querySelectorAll('img');

                    if (!images || images.length === 0) {
                        Utils.log('warn', '未找到图片元素', albumArea);
                    } else {
                        Utils.log('log', `找到${images.length}张图片`);
                    }

                    const imageUrls = Array.from(images).map(img => img.src);
                    this.selectedGroups.set(reviewIndex, imageUrls);
                    this.updateSelectedCount();
                }, CONFIG.delays.imageLoad);
            } else {
                this.selectedGroups.delete(reviewIndex);
                this.updateSelectedCount();
            }
        }

        // 更新选中数量
        updateSelectedCount() {
            const count = this.selectedGroups.size;
            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = count;
            }
        }

        // 处理转移
        async handleTransfer() {
            if (this.selectedGroups.size === 0) {
                alert('请至少选择一组图片');
                return;
            }

            try {
                // 更新按钮状态为处理中
                this.updateButtonState(true);

                // 确保所有选中的评价都已加载
                await this.ensureReviewsLoaded();

                // 重新收集所有选中评价的图片
                this.selectedGroups.clear();
                const checkboxes = document.querySelectorAll(`.${CONFIG.classNames.checkbox}:checked`);
                for (const checkbox of checkboxes) {
                    // 查找评价项
                    const review = checkbox.closest('[class*="--Comment--"]');
                    if (!review) continue;

                    // 查找图片区域
                    const albumArea = review.querySelector('[class*="--album--"]');
                    if (!albumArea) continue;

                    // 查找图片元素
                    const images = albumArea.querySelectorAll('img');

                    const reviewIndex = checkbox.dataset.reviewIndex;
                    const imageUrls = Array.from(images).map(img => img.src);
                    this.selectedGroups.set(reviewIndex, imageUrls);
                }

                // 将Map转换为数组，保持顺序
                const reviews = Array.from(this.selectedGroups.entries())
                    .sort((a, b) => Number(a[0]) - Number(b[0]))
                    .map(([_, images]) => ({ images }));

                // 发送消息到background script
                const response = await chrome.runtime.sendMessage({
                    type: 'processReviews',
                    reviews: reviews
                });

                if (response?.success) {
                    Utils.log('log', '转移请求已发送');
                } else {
                    throw new Error('发送转移请求失败');
                }
            } catch (error) {
                Utils.log('error', '处理转移失败:', error);
                alert('转移失败，请重试');
            } finally {
                // 处理完成后更新按钮状态
                this.updateButtonState(false);
            }
        }

        // 确保所有评价都已加载
        async ensureReviewsLoaded() {
            return new Promise((resolve) => {
                // 查找评价容器
                const container = Utils.findElement(CONFIG.selectors.reviewsContainer);

                if (!container) {
                    resolve();
                    return;
                }

                // 获取所有选中的复选框
                const selectedCheckboxes = Array.from(document.querySelectorAll(`.${CONFIG.classNames.checkbox}:checked`));
                if (selectedCheckboxes.length === 0) {
                    resolve();
                    return;
                }

                // 获取最后一个选中的评价元素
                const lastSelectedReview = selectedCheckboxes[selectedCheckboxes.length - 1].closest('[class*="--Comment--"]');

                if (!lastSelectedReview) {
                    Utils.log('warn', '无法找到最后一个选中的评价');
                    resolve();
                    return;
                }

                // 创建一个Intersection Observer来监测最后一个选中的评价是否可见
                const observer = new IntersectionObserver((entries) => {
                    const lastEntry = entries[0];
                    if (lastEntry.isIntersecting) {
                        observer.disconnect();
                        // 给一点时间让图片完全加载
                        setTimeout(resolve, 1000);
                    }
                }, {
                    root: container,
                    threshold: 1.0
                });

                // 开始观察最后一个选中的评价
                observer.observe(lastSelectedReview);

                // 自动滚动到最后一个选中的评价
                lastSelectedReview.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // 设置超时，防止无限等待
                setTimeout(() => {
                    observer.disconnect();
                    resolve();
                }, CONFIG.delays.loadTimeout);
            });
        }

        // 处理终止按钮的显示和隐藏
        updateButtonState(isProcessing) {
            const transferButton = document.getElementById('autoTransfer');
            const stopButton = document.getElementById('stopTransfer');

            if (isProcessing) {
                transferButton.style.display = 'none';
                stopButton.style.display = 'inline-block';
            } else {
                transferButton.style.display = 'inline-block';
                stopButton.style.display = 'none';
            }
        }

        // 处理复制图片
        async handleCopyImages(albumArea) {
            try {
                const images = albumArea.querySelectorAll('img');
                if (!images || images.length === 0) {
                    Utils.log('warn', '未找到图片元素，无法复制');
                    alert('未找到图片，无法复制');
                    return;
                }

                Utils.log('log', `尝试复制 ${images.length} 张图片到剪贴板`);

                // 显示复制中状态
                const statusIndicator = this.showStatusIndicator(albumArea, '复制中...');

                try {
                    // 对于单张图片，尝试直接复制图片内容到剪贴板
                    if (images.length === 1) {
                        const img = images[0];

                        // 尝试使用较大的图片URL
                        const originalUrl = this.getOriginalImageUrl(img.src);
                        Utils.log('log', `处理后的原始URL: ${originalUrl}`);

                        try {
                            // 使用fetch请求实际获取图片内容
                            const response = await fetch(originalUrl, {
                                headers: {
                                    'Accept': 'image/*'
                                },
                                mode: 'cors',
                                cache: 'no-cache'
                            });

                            if (!response.ok) {
                                throw new Error(`获取图片失败: ${response.status}`);
                            }

                            // 获取图片blob
                            const blob = await response.blob();

                            // 尝试复制到剪贴板
                            try {
                                // 尝试使用现代Clipboard API
                                await navigator.clipboard.write([
                                    new ClipboardItem({ [blob.type]: blob })
                                ]);

                                this.updateStatusIndicator(statusIndicator, '复制成功', 'success');
                                Utils.log('log', '图片已复制到剪贴板');
                            } catch (clipboardError) {
                                // 如果Clipboard API失败，复制URL作为备选
                                Utils.log('warn', '复制到剪贴板失败，尝试复制URL:', clipboardError);
                                this.copyTextToClipboard(originalUrl);
                                this.updateStatusIndicator(statusIndicator, '已复制图片链接', 'warning');
                            }
                        } catch (fetchError) {
                            Utils.log('error', '获取图片内容失败:', fetchError);
                            // 作为备选，复制URL
                            this.copyTextToClipboard(originalUrl);
                            this.updateStatusIndicator(statusIndicator, '已复制图片链接', 'warning');
                        }
                    } else {
                        // 对于多张图片，目前只能复制所有图片的URL（浏览器限制）
                        Utils.log('log', '多张图片复制为URL列表');
                        const urls = Array.from(images).map(img => this.getOriginalImageUrl(img.src));
                        this.copyTextToClipboard(urls.join('\n'));
                        this.updateStatusIndicator(statusIndicator, `已复制 ${urls.length} 张图片链接`, 'success');
                        Utils.log('log', `已复制 ${urls.length} 张图片URL到剪贴板`);
                    }
                } catch (error) {
                    Utils.log('error', '复制图片过程出错:', error);
                    this.updateStatusIndicator(statusIndicator, '复制失败', 'error');
                }
            } catch (error) {
                Utils.log('error', '复制图片时发生错误:', error);
                alert('复制图片失败: ' + error.message);
            }
        }

        // 处理下载图片
        handleDownloadImages(albumArea) {
            try {
                const images = albumArea.querySelectorAll('img');
                if (!images || images.length === 0) {
                    Utils.log('warn', '未找到图片元素，无法下载');
                    alert('未找到图片，无法下载');
                    return;
                }

                Utils.log('log', `准备下载 ${images.length} 张图片`);

                // 显示下载中状态
                const statusIndicator = this.showStatusIndicator(albumArea, '下载中...');

                // 获取当前商品ID作为文件名前缀
                const itemId = this.getItemId() || 'taobao';
                const timestamp = new Date().getTime();

                // 批量下载所有图片
                let downloadCount = 0;
                let errorCount = 0;

                Array.from(images).forEach((img, imgIndex) => {
                    try {
                        // 获取原始大图链接
                        const originalUrl = this.getOriginalImageUrl(img.src);
                        Utils.log('log', `处理后的下载URL: ${originalUrl}`);

                        // 使用chrome.downloads API (通过background脚本)
                        chrome.runtime.sendMessage({
                            type: 'downloadImage',
                            url: originalUrl,
                            filename: `${itemId}_${timestamp}_${imgIndex + 1}.jpg`
                        }, (response) => {
                            if (response && response.success) {
                                downloadCount++;
                                Utils.log('log', `下载成功: 图片 ${imgIndex + 1}`);

                                if (downloadCount + errorCount === images.length) {
                                    // 全部处理完毕，更新状态
                                    if (errorCount === 0) {
                                        this.updateStatusIndicator(statusIndicator, `已下载 ${downloadCount} 张图片`, 'success');
                                    } else {
                                        this.updateStatusIndicator(statusIndicator, `已下载 ${downloadCount} 张图片，失败 ${errorCount} 张`, 'warning');
                                    }
                                }
                            } else {
                                errorCount++;
                                Utils.log('error', `下载失败: 图片 ${imgIndex + 1} - ${response ? response.error : '未知错误'}`);

                                if (downloadCount + errorCount === images.length) {
                                    // 全部处理完毕，更新状态
                                    this.updateStatusIndicator(statusIndicator, `已下载 ${downloadCount} 张图片，失败 ${errorCount} 张`, 'warning');
                                }
                            }
                        });
                    } catch (error) {
                        Utils.log('error', `下载图片 ${imgIndex + 1} 失败:`, error);
                        errorCount++;

                        if (downloadCount + errorCount === images.length) {
                            // 全部处理完毕，更新状态
                            this.updateStatusIndicator(statusIndicator, `已下载 ${downloadCount} 张图片，失败 ${errorCount} 张`, 'warning');
                        }
                    }
                });

                // 如果发送消息后还未收到任何回调，显示初始状态
                this.updateStatusIndicator(statusIndicator, `正在下载 ${images.length} 张图片...`, 'info');

                Utils.log('log', `已启动 ${images.length} 张图片的下载`);
            } catch (error) {
                Utils.log('error', '下载图片时发生错误:', error);
                alert('下载图片失败: ' + error.message);
            }
        }

        // 获取商品ID
        getItemId() {
            const url = window.location.href;
            const match = url.match(/item\.htm\?.*?id=(\d+)/);
            return match ? match[1] : '';
        }

        // 获取原始图片URL（获取大图）
        getOriginalImageUrl(url) {
            if (!url) return '';

            Utils.log('log', `处理前URL: ${url}`);

            // 淘宝图片链接通常包含尺寸信息，我们需要获取原始大图
            let originalUrl = url;

            // 先移除查询参数
            originalUrl = originalUrl.replace(/\?.*$/, '');

            // 专门处理淘宝图片中常见的尺寸格式: _960x960.jpg
            originalUrl = originalUrl.replace(/\_\d+x\d+\.jpg/g, '');

            // 处理类似 _180x180.jpg_960x960.jpg_.webp 这样的多重尺寸后缀
            originalUrl = originalUrl.replace(/\_\d+x\d+\.jpg\_\d+x\d+\.jpg/g, '');

            // 处理 .jpg_.webp 格式
            originalUrl = originalUrl.replace(/\.jpg_\.webp/g, '.jpg');

            // 处理 .jpg.jpg 双重后缀
            originalUrl = originalUrl.replace(/\.jpg\.jpg/g, '.jpg');

            // 提取第一个有效扩展名
            const baseUrlMatch = originalUrl.match(/^(.*?)\.(jpe?g|png|gif|webp)/i);
            if (baseUrlMatch) {
                // 使用基础URL和第一个扩展名重构URL
                originalUrl = `${baseUrlMatch[1]}.${baseUrlMatch[2]}`;
            }

            // 兜底替换任何剩余的特殊格式
            originalUrl = originalUrl.replace(/\_\.webp$/i, '');
            originalUrl = originalUrl.replace(/\_\d+x\d+/g, '');

            Utils.log('log', `处理后URL: ${originalUrl}`);

            return originalUrl;
        }

        // 显示状态指示器
        showStatusIndicator(albumArea, message) {
            const indicator = Utils.createElement('div', {
                text: message,
                css: `
                    position: absolute;
                    top: 40px;
                    right: 10px;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                    z-index: 10000;
                `
            });

            albumArea.appendChild(indicator);

            // 5秒后自动移除
            setTimeout(() => {
                if (indicator && indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 5000);

            return indicator;
        }

        // 更新状态指示器
        updateStatusIndicator(indicator, message, type = 'info') {
            if (!indicator || !indicator.parentNode) return;

            // 根据类型设置颜色
            let color = '#1E9FFF'; // 默认蓝色（信息）
            if (type === 'success') color = '#009688'; // 绿色
            if (type === 'warning') color = '#FFB800'; // 黄色
            if (type === 'error') color = '#FF5722';   // 红色

            indicator.textContent = message;
            indicator.style.background = `rgba(0, 0, 0, 0.7)`;
            indicator.style.borderLeft = `3px solid ${color}`;
        }

        // 复制文本到剪贴板
        copyTextToClipboard(text) {
            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed'; // 避免滚动到底部
            textarea.style.opacity = '0';

            document.body.appendChild(textarea);
            textarea.select();

            try {
                // 执行复制命令
                const successful = document.execCommand('copy');
                Utils.log('log', successful ? '文本复制成功' : '文本复制失败');
            } catch (err) {
                Utils.log('error', '复制文本失败:', err);
                // 尝试使用现代API
                try {
                    navigator.clipboard.writeText(text);
                    Utils.log('log', '使用Clipboard API复制成功');
                } catch (clipboardError) {
                    Utils.log('error', 'Clipboard API复制失败:', clipboardError);
                }
            }

            document.body.removeChild(textarea);
        }
    }

    // 淘宝链接处理器
    class TaobaoLinkHandler {
        constructor() {
            this.button = null;
            this.targetUrl = CONFIG.targetSite.url;
        }

        // 添加复制按钮
        addCopyButton() {
            if (this.button) return;

            this.button = Utils.createElement('div', {
                html: '',
                css: CONFIG.styles.copyButton,
                events: {
                    mouseover: () => {
                        this.button.style.transform = 'scale(1.1)';
                    },
                    mouseout: () => {
                        this.button.style.transform = 'scale(1)';
                    },
                    click: () => this.handleClick()
                }
            });

            document.body.appendChild(this.button);
            Utils.log('log', '复制按钮已添加');
        }

        // 获取简化的URL
        getSimplifiedUrl() {
            const url = window.location.href;
            const match = url.match(/item\.htm\?id=(\d+)/);
            return match ? `https://item.taobao.com/item.htm?id=${match[1]}` : url;
        }

        // 获取价格
        getPrice() {
            // 尝试查找价格元素
            for (const selector of CONFIG.selectors.priceElement) {
                const priceElement = document.querySelector(selector);
                if (priceElement) {
                    Utils.log('log', `已找到价格元素: ${selector}`);
                    return priceElement.textContent;
                }
            }

            Utils.log('warn', '未找到价格元素');
            return '';
        }

        // 处理点击事件
        async handleClick() {
            const url = this.getSimplifiedUrl();
            const price = this.getPrice();

            Utils.log('log', `复制链接: ${url}, 价格: ${price}`);

            // 使用已有的标签页查询功能
            chrome.runtime.sendMessage({
                type: 'processReviews',
                action: 'checkTab',
                data: { url, price }
            }, (response) => {
                if (response?.success) {
                    Utils.log('log', '链接复制成功');
                } else {
                    Utils.log('error', '链接复制失败');
                }
            });
        }
    }

    /**
     * 初始化应用
     * 创建评价收集器和链接处理器
     */
    function initializeApp() {
        Utils.log('log', '初始化应用');

        const collector = new ImageCollector();
        collector.initializeInterface();

        // 添加链接处理功能
        const linkHandler = new TaobaoLinkHandler();
        linkHandler.addCopyButton();

        Utils.log('log', '应用初始化完成');
    }

    // 在页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }
})();