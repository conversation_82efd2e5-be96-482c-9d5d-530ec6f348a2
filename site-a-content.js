(function() {
    'use strict';
    
    console.log('网站A内容脚本已加载');

    // AI功能配置
    const AI_CONFIG = {
        // SiliconFlow API 配置
        API_URL: 'https://api.siliconflow.cn/v1/chat/completions',
        MODEL: 'Qwen/QwQ-32B-Preview',
        API_KEY: 'sk-rgjclhvsomldsqiazlccwuwoasboxikminjrvtlblnlsfuvb', // 重要：请替换为你的API密钥，或实现安全的密钥管理方式
        
        // 按钮和UI配置
        BUTTON_TEXT: 'AI分析标题',
        BUTTON_STYLE: `
            margin-left: 10px;
            margin-bottom: 5px;
            padding: 0 10px;
            height: 28px;
            line-height: 28px;
            border: 1px solid #1E9FFF;
            background-color: #1E9FFF;
            color: white;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        `,
        RESULT_CONTAINER_STYLE: `
            margin-top: 10px;
            padding: 10px;
            border: 1px dashed #ddd;
            border-radius: 3px;
            background-color: #f9f9f9;
            font-size: 12px;
            display: none;
        `,
        LOADING_TEXT: '正在分析标题并生成关键词，请稍候...',
        ERROR_TEXT: '分析失败，请重试',
        RESULT_HEADER: '🔍 AI推荐关键词：',
        
        // 提示词模板
        PROMPT_TEMPLATE: `分析以下淘宝商品标题，生成{count}个搜索关键词：
"{title}"

严格要求：
1. 必须生成且只生成{count}个关键词，不多不少
2. 每个关键词必须包含主体商品名称
3. 每个关键词5-10个字之间
4. 每个关键词不能重复
5. 关键词要突出商品特点
6. 必须严格按照如下JSON格式返回（不要有任何解释、序号或额外文本）：
["关键词1","关键词2","关键词3",...]`,

        // 选择器配置
        SELECTORS: {
            // 尝试多种可能的商品名称输入框选择器
            PRODUCT_NAME_INPUT: 'input[name="item.GoodsName"], input[lay-verify="required|title"], .layui-input[placeholder*="商品名称"]',
            // 更新关键词输入框选择器，包含更多可能的匹配模式
            KEYWORD_INPUT: 'input[lay-verify="req"][placeholder*="搜索关键词"], input.layui-input[placeholder*="搜索关键词"], .task_keyword_box input[type="text"], div.layui-input-inline input[type="text"][name*="keyword"], input[name="item.SearchKeywordType"]',
            TASK_COUNT_CONTAINER: 'li[name^="lstPicTask"]:last-child'
        },
        
        // 调试模式
        DEBUG: true
    };

    // AI功能管理器
    class AIFeatureManager {
        constructor() {
            // 基础设置
            this.initialized = false;
            this.taskCount = 0;
        }
        
        initialize() {
            console.log('初始化AI功能管理器');
            
            if (this.initialized) {
                console.log('AI功能管理器已初始化，不重复执行');
                return;
            }
            
            try {
                // 不再添加分析按钮，改为仅更新任务数量
                // this.addAnalysisButton(); - 不再调用此方法
                
                // 获取任务数量
                this.updateTaskCount();
                
                this.initialized = true;
                console.log('AI功能管理器初始化完成');
            } catch (error) {
                console.error('AI功能管理器初始化失败:', error);
            }
        }
        
        // 完全禁用按钮创建功能
        addAnalysisButton() {
            console.log('【禁用功能】分析按钮创建功能已完全禁用');
            // 不执行任何操作，确保不会创建按钮
            return false;
        }
        
        updateTaskCount() {
            // 获取任务数量的逻辑（保留这个，因为directAnalyzeTitle也会用到类似逻辑）
            try {
                // 首先尝试通过lstPicTask元素计算
                const taskContainers = document.querySelectorAll('li[name^="lstPicTask"]');
                if (taskContainers && taskContainers.length > 0) {
                    this.taskCount = taskContainers.length;
                    console.log(`通过lstPicTask元素检测到任务数量: ${this.taskCount}`);
                    return;
                }
                
                // 如果上面的方法失败，尝试通过"搜索关键词"标签数量计算
                const searchLabels = Array.from(document.querySelectorAll('label')).filter(label => 
                    label.textContent.trim().includes('搜索关键词') || 
                    label.textContent.trim().includes('搜索词')
                );
                
                if (searchLabels && searchLabels.length > 0) {
                    this.taskCount = searchLabels.length;
                    console.log(`通过"搜索关键词"标签检测到任务数量: ${this.taskCount}`);
                    return;
                }
                
                // 备用方法：通过页面文本
                const textElements = document.querySelectorAll('.layui-elem-field legend, .strong');
                for (const element of textElements) {
                    const match = element.textContent.match(/第(\d+)单/);
                    if (match && match[1]) {
                        const count = parseInt(match[1]);
                        if (!isNaN(count) && count > 0) {
                            this.taskCount = count; 
                            console.log(`通过页面文本找到任务数量: ${this.taskCount}`);
                            break;
                        }
                    }
                }
                
                // 如果所有方法都失败，使用默认值
                this.taskCount = 5;
                console.log(`未能检测到任务数量，使用默认值: ${this.taskCount}`);
            } catch (error) {
                console.error('更新任务数量时出错:', error);
                this.taskCount = 5; // 出错时使用默认值
            }
        }
        
        // 其他方法都不再需要，已被directAnalyzeTitle替代
    }

    // 图片上传管理器
    class ImageUploader {
        constructor() {
            this.maxRetries = 3;
            this.retryDelay = 500;
            this.uploadedImages = new Set();
            
            this.groupColors = [
                '#f9f9ff', // 浅紫色
                '#f9fff9', // 浅绿色
                '#fff9f9', // 浅红色
                '#f9ffff', // 浅蓝色
                '#fffff9'  // 浅黄色
            ];
            this.isProcessing = false;
            this.shouldStop = false;
            this.controlPanel = new ControlPanelManager();
        }

        // 为主组添加背景色
        setGroupBackground(mainGroup, index) {
            const colorIndex = index % this.groupColors.length;
            mainGroup.style.backgroundColor = this.groupColors[colorIndex];
            mainGroup.style.padding = '10px';
            mainGroup.style.borderRadius = '5px';
            mainGroup.style.marginBottom = '10px';
        }

        // 初始化所有主组的背景色
        initializeGroupBackgrounds() {
            const mainGroups = document.querySelectorAll('li[name^="lstPicTask"]');
            mainGroups.forEach((group, index) => {
                this.setGroupBackground(group, index);
            });

            // 监听DOM变化，为新添加的组添加背景色
            const observer = new MutationObserver((mutations) => {
                mutations.forEach(() => {
                    const groups = document.querySelectorAll('li[name^="lstPicTask"]');
                    groups.forEach((group, index) => {
                        this.setGroupBackground(group, index);
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        // 从您的备份文件中复制所有这些方法
        async waitForElement(selector, timeout = 5000) {
            return new Promise((resolve, reject) => {
                const observer = new MutationObserver((mutations, obs) => {
                    const element = document.querySelector(selector);
                    if (element) {
                        obs.disconnect();
                        resolve(element);
                    }
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                setTimeout(() => {
                    observer.disconnect();
                    reject(new Error(`等待元素超时: ${selector}`));
                }, timeout);
                
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });
        }

        async ensureActivitySelected() {
            try {
                const checkbox = await this.waitForElement('input[name="lstPicTask"][value="1"]');
                if (!checkbox.checked) {
                    checkbox.click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                return true;
            } catch (error) {
                console.error('选择图文报告活动失败:', error);
                throw error;
            }
        }

        async addNewGroup() {
            try {
                const addButton = await this.waitForElement('.btn-extra-comments');
                addButton.click();
                await new Promise(resolve => setTimeout(resolve, 500));
                return true;
            } catch (error) {
                console.error('添加新组失败:', error);
                return false;
            }
        }

        async waitForUploadComplete(uploadArea, file) {
            return new Promise((resolve, reject) => {
                let checkCount = 0;
                const maxChecks = 30;
                const checkInterval = 100;

                const checkUpload = () => {
                    const previewContainer = uploadArea.parentElement.querySelector('.layui-upload-drag.nonebd');
                    const images = previewContainer ? previewContainer.querySelectorAll('img') : [];
                    
                    if (images.length > 0) {
                        console.log('检测到图片上传成功');
                        setTimeout(() => resolve(true), 100);
                        return;
                    }

                    checkCount++;
                    if (checkCount >= maxChecks) {
                        console.log('上传检测超时');
                        reject(new Error('上传超时'));
                        return;
                    }

                    setTimeout(checkUpload, checkInterval);
                };

                checkUpload();
            });
        }

        async simulateFileSelect(file, uploadArea) {
            return new Promise(async (resolve, reject) => {
                try {
                    const fileId = file.name + file.size;
                    if (this.uploadedImages.has(fileId)) {
                        console.log('图片已经上传过，跳过');
                        resolve(true);
                        return;
                    }

                    const input = uploadArea.parentElement.querySelector('.layui-upload-file');
                    if (!input) {
                        throw new Error('未找到文件输入元素');
                    }

                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    input.files = dataTransfer.files;

                    const changeEvent = new Event('change', { bubbles: true });
                    input.dispatchEvent(changeEvent);

                    const success = await this.waitForUploadComplete(uploadArea, file);
                    if (success) {
                        this.uploadedImages.add(fileId);
                        resolve(true);
                    } else {
                        reject(new Error('上传超时'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }

        async compressImage(file, options = {}) {
            const {
                maxWidth = 1920,
                maxHeight = 1920,
                quality = 0.7,
                maxSizeKB = 500,
            } = options;

            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        let width = img.width;
                        let height = img.height;
                        
                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        const canvas = document.createElement('canvas');
                        canvas.width = width;
                        canvas.height = height;
                        
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);

                        const originalSize = file.size;

                        canvas.toBlob((blob) => {
                            console.log('压缩信息:', {
                                原始大小: `${(originalSize / 1024).toFixed(2)}KB`,
                                压缩后大小: `${(blob.size / 1024).toFixed(2)}KB`,
                                压缩比例: `${((1 - blob.size / originalSize) * 100).toFixed(1)}%`,
                                质量设置: quality,
                                尺寸: `${width}x${height}`
                            });
                            
                            resolve(blob);
                        }, 'image/jpeg', quality);
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

        async uploadImage(imageUrl, uploadArea, retryCount = 0) {
            try {
                console.log('开始处理图片:', imageUrl);
                
                const response = await fetch(imageUrl);
                if (!response.ok) throw new Error(`获取图片失败: ${response.status}`);
                
                const blob = await response.blob();
                
                const compressedBlob = await this.compressImage(blob, {
                    maxWidth: 1920,
                    maxHeight: 1920,
                    quality: 0.7,
                    maxSizeKB: 500
                });
                
                const file = new File([compressedBlob], `image_${Date.now()}.jpg`, { type: 'image/jpeg' });

                await this.simulateFileSelect(file, uploadArea);
                await this.waitForUploadComplete(uploadArea, file);
                
                console.log('图片上传成功');
                return true;
            } catch (error) {
                if (retryCount < this.maxRetries) {
                    console.log(`上传失败，第 ${retryCount + 1} 次重试`);
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                    return this.uploadImage(imageUrl, uploadArea, retryCount + 1);
                }
                throw error;
            }
        }

        async findAvailableUploadArea() {
            const mainGroups = document.querySelectorAll('li[name="lstPicTask"]');
            
            const subProducts = document.querySelectorAll('div[data-imgs=""][name^="CommentImg_"].layui-upload-drag.nonebd');
            console.log('找到的子商品上传区域数量:', subProducts.length);
            
            if (subProducts.length > 0) {
                console.log('检测到子商品上传区域');
                for (const subArea of subProducts) {
                    const existingImages = subArea.querySelectorAll('img');
                    if (existingImages.length === 0) {
                        const name = subArea.getAttribute('name');
                        console.log(`找到空的子商品上传区域: ${name}`);
                        const uploadBox = subArea.closest('.upload-box');
                        if (uploadBox) {
                            const goodIndex = uploadBox.querySelector(`[goodindex="${name}"]`);
                            if (goodIndex) {
                                return subArea;
                            }
                        }
                    }
                }
            }
            
            console.log('未找到可用的子商品上传区域，使用主商品逻辑');
            let targetIndex = 0;
            let uploadArea = null;
            let foundEmptyGroup = false;
            
            while (!foundEmptyGroup) {
                uploadArea = document.querySelector(
                    `div[name-field="lstPicTask"][name="CommentImg"][uploadindex="${targetIndex}"]`
                );

                if (!uploadArea) {
                    console.log(`创建新组，索引: ${targetIndex}`);
                    const success = await this.addNewGroup();
                    if (!success) throw new Error(`创建新组失败，索引: ${targetIndex}`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    uploadArea = await this.waitForElement(
                        `div[name-field="lstPicTask"][name="CommentImg"][uploadindex="${targetIndex}"]`
                    );
                    foundEmptyGroup = true;
                } else {
                    const existingImages = uploadArea.parentElement.querySelectorAll('.layui-upload-drag.nonebd img');
                    if (existingImages.length === 0) {
                        console.log(`找到空组，索引: ${targetIndex}`);
                        foundEmptyGroup = true;
                    } else {
                        console.log(`索引 ${targetIndex} 的组已有图片，继续查找`);
                        targetIndex++;
                    }
                }
            }

            if (!uploadArea) {
                throw new Error(`未找到可用的上传区域`);
            }

            return uploadArea;
        }

        async processAllGroups(imageGroups) {
            try {
                this.isProcessing = true;
                this.shouldStop = false;
                
                // 显示控制面板
                this.controlPanel.showPanel();

                await this.ensureActivitySelected();

                for (let i = 0; i < imageGroups.length; i++) {
                    if (this.shouldStop) {
                        console.log('用户终止了上传');
                        return { success: false, reason: 'user_stopped' };
                    }

                    console.log(`处理第 ${i + 1} 组图片`);

                    let uploadArea = await this.findAvailableUploadArea();

                    if (!uploadArea) {
                        console.log('创建新的主组');
                        const success = await this.addNewGroup();
                        if (!success) throw new Error('创建新组失败');
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        uploadArea = await this.findAvailableUploadArea();
                        if (!uploadArea) {
                            throw new Error('无法找到可用的上传区域');
                        }
                    }

                    console.log(`使用上传区域，索引: ${uploadArea.getAttribute('uploadindex')}`);

                    const group = imageGroups[i];
                    for (let imageIndex = 0; imageIndex < group.images.length; imageIndex++) {
                        const imageUrl = group.images[imageIndex];
                        try {
                            await this.uploadImage(imageUrl, uploadArea);
                            await new Promise(resolve => setTimeout(resolve, 100));
                        } catch (error) {
                            console.error(`上传图片失败: ${imageUrl}`, error);
                        }
                    }
                }

                return { success: true };
            } catch (error) {
                console.error('处理图片组失败:', error);
                throw error;
            } finally {
                this.isProcessing = false;
                // 隐藏控制面板
                this.controlPanel.hidePanel();
            }
        }

        // 添加终止方法
        stopProcessing() {
            this.shouldStop = true;
            console.log('收到终止请求');
        }
    }

    // 粘贴功能管理器
    class PasteAreaManager {
        constructor() {
            this.uploadButtonSelector = '.layui-upload-drag:not(.nonebd)';
        }

        addPasteArea(uploadButton) {
            // 检查是否已添加粘贴区域
            if (uploadButton.nextElementSibling?.classList.contains('paste-area')) {
                return;
            }

            // 创建粘贴区域
            const pasteArea = document.createElement('div');
            pasteArea.classList.add('paste-area');
            pasteArea.style.cssText = `
                border: 2px dashed rgb(204, 204, 204);
                padding: 10px;
                margin-top: 0px;
                text-align: center;
                cursor: pointer;
                width: 50px;
                height: 100px;
                display: inline-block;
                vertical-align: top;
                margin-left: 10px;
                font-size: 12px;
                color: rgb(102, 102, 102);
            `;
            pasteArea.textContent = '点击粘贴';
            pasteArea.tabIndex = 0;

            // 点击时自动聚焦
            pasteArea.addEventListener('click', () => {
                pasteArea.focus();
            });

            // 处理粘贴事件
            pasteArea.addEventListener('paste', (event) => {
                const items = event.clipboardData.items;
                let hasImage = false;
                const imageFiles = [];

                for (let item of items) {
                    if (item.type.startsWith('image/')) {
                        hasImage = true;
                        const file = item.getAsFile();
                        imageFiles.push(file);
                    }
                }

                if (hasImage) {
                    event.preventDefault();

                    // 创建 DataTransfer 对象
                    const dataTransfer = new DataTransfer();
                    imageFiles.forEach(file => {
                        dataTransfer.items.add(file);
                    });

                    // 创建拖拽事件
                    const dragEvent = new DragEvent('drop', {
                        dataTransfer: dataTransfer,
                        bubbles: true,
                        cancelable: true
                    });

                    // 直接触发拖拽事件
                    uploadButton.dispatchEvent(dragEvent);
                }
            });

            // 插入粘贴区域
            uploadButton.parentNode.insertBefore(pasteArea, uploadButton.nextSibling);
        }

        initialize() {
            // 找到所有上传按钮
            const uploadButtons = document.querySelectorAll(this.uploadButtonSelector);
            uploadButtons.forEach(button => {
                this.addPasteArea(button);
            });

            // 监听新增的上传区域
            const observer = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList') {
                        const newButtons = document.querySelectorAll(this.uploadButtonSelector);
                        newButtons.forEach(button => {
                            if (!button.nextElementSibling?.classList.contains('paste-area')) {
                                this.addPasteArea(button);
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // 评价内容管理器
    class CommentManager {
        constructor() {
            this.comments = [];
            this.usedComments = new Set();
            this.initialized = false;
        }

        // 初始化评价内容
        async initialize() {
            if (this.initialized) return;
            
            try {
                // 从扩展中读取评价文件
                const response = await fetch(chrome.runtime.getURL('pingyu.txt'));
                const text = await response.text();
                // 分割成行并过滤空行
                this.comments = text.split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
                
                console.log(`成功加载${this.comments.length}条评价内容`);
                this.initialized = true;
            } catch (error) {
                console.error('加载评价内容失败:', error);
            }
        }

        // 获取一个未使用的随机评价
        getRandomComment() {
            if (!this.initialized || this.comments.length === 0) {
                return '';
            }

            // 过滤出未使用的评价
            const availableComments = this.comments.filter(comment => !this.usedComments.has(comment));
            
            // 如果所有评价都已使用，返回空字符串
            if (availableComments.length === 0) {
                console.log('所有评价内容已用完');
                return '';
            }

            // 随机选择一条评价
            const randomIndex = Math.floor(Math.random() * availableComments.length);
            const selectedComment = availableComments[randomIndex];
            
            // 标记为已使用
            this.usedComments.add(selectedComment);
            
            return selectedComment;
        }

        // 重置已使用的评价记录
        reset() {
            this.usedComments.clear();
        }
    }

    // 数据填充管理器
    class DataFiller {
        constructor() {
            this.initialized = false;
            this.existingUrls = new Set();
            this.commentManager = new CommentManager();
        }

        // 创建悬浮按钮
        createFloatingButton() {
            console.log('开始创建按钮...');
            
            // 创建按钮容器
            const container = document.createElement('div');
            container.style.cssText = `
                position: fixed;
                right: 100px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                flex-direction: column;
                gap: 10px;
                z-index: 19891019;
            `;

            // 创建填充评价按钮
            const commentButton = this.createButton('📝', '填充评价', () => {
                this.fillRandomComment();
            });

            // 创建填充输入框按钮
            const inputButton = this.createButton('📋', '填充输入', () => {
                this.fillInputs();
            });

            // 添加按钮到容器
            container.appendChild(commentButton);
            container.appendChild(inputButton);

            // 添加到body
            document.body.appendChild(container);
            console.log('按钮已创建');
        }

        // 创建单个按钮的辅助方法
        createButton(icon, title, onClick) {
            const button = document.createElement('button');
            button.className = 'layui-btn';
            button.type = 'button';
            button.innerHTML = icon;
            button.title = title;
            button.style.cssText = `
                width: 40px;
                height: 40px;
                padding: 0;
                line-height: 40px;
                font-size: 20px;
                text-align: center;
                border-radius: 4px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                background-color: #1E9FFF;
                border: none;
                cursor: pointer;
                pointer-events: auto;
            `;

            // 悬停效果
            button.onmouseover = () => {
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 4px 10px rgba(0,0,0,0.3)';
            };
            button.onmouseout = () => {
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            };

            // 点击效果
            button.onmousedown = () => {
                button.style.transform = 'scale(0.95)';
            };
            button.onmouseup = () => {
                button.style.transform = 'scale(1.1)';
            };

            // 点击事件
            button.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                onClick();
                // 点击后的视觉反馈
                button.style.transform = 'scale(1)';
                setTimeout(() => {
                    button.style.transform = 'scale(1.1)';
                }, 100);
            };

            return button;
        }

        // 等待元素加载
        async waitForElement(selector, timeout = 5000) {
            const start = Date.now();
            
            while (Date.now() - start < timeout) {
                const element = document.querySelector(selector);
                if (element) {
                    return element;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            throw new Error(`等待元素超时: ${selector}`);
        }

        // 检查是否已存在该URL
        isUrlExists(url) {
            const urlInputs = document.querySelectorAll('.layui-input.goods_url');
            for (const input of urlInputs) {
                if (input.value === url) {
                    return true;
                }
            }
            return false;
        }

        // 获取当前商品数量
        getGoodsCount() {
            // 计算已填写URL的商品数量
            const urlInputs = document.querySelectorAll('.layui-input.goods_url');
            let count = 0;
            for (const input of urlInputs) {
                if (input.value) {
                    count++;
                }
            }
            return count;
        }

        // 点击增加附加商品按钮
        clickAddButton() {
            const addButton = document.querySelector('#addgoodsBtn');
            if (addButton) {
                addButton.click();
                return true;
            }
            return false;
        }

        // 获取最后一个空的商品URL输入框
        getLastEmptyUrlInput() {
            const urlInputs = document.querySelectorAll('.layui-input.goods_url');
            for (const input of urlInputs) {
                if (!input.value) {
                    return input;
                }
            }
            return null;
        }

        // 填充URL和价格
        fillData(data) {
            console.log('开始填充数据:', data);

            // 如果URL已存在，直接返回
            if (this.isUrlExists(data.url)) {
                console.log('URL已存在，跳过添加');
                return;
            }

            // 获取当前商品数量
            const goodsCount = this.getGoodsCount();
            console.log('当前已填写商品数量:', goodsCount);
            if (goodsCount >= 5) {
                console.log('商品数量已达到上限（5个）');
                return;
            }

            // 检查是否需要点击添加按钮
            let targetInput = this.getLastEmptyUrlInput();
            if (!targetInput && goodsCount < 5) {
                if (!this.clickAddButton()) {
                    console.log('无法找到添加按钮');
                    return;
                }
                // 等待新元素加载
                setTimeout(() => {
                    targetInput = this.getLastEmptyUrlInput();
                    if (targetInput) {
                        this.fillDataToInput(targetInput, data);
                    }
                }, 500);
            } else {
                this.fillDataToInput(targetInput, data);
            }
        }

        // 填充数据到指定输入框
        fillDataToInput(urlInput, data) {
            // 获取当前商品区域
            const fieldset = urlInput.closest('fieldset.layui-elem-field');
            if (!fieldset) {
                console.log('无法找到商品区域');
                return;
            }

            // 填充URL
            urlInput.value = data.url;
            this.triggerLayuiEvents(urlInput);

            // 填充价格和数量
            setTimeout(() => {
                // 在当前fieldset中查找价格输入框
                const priceInputs = fieldset.querySelectorAll('.layui-input.minW');
                priceInputs.forEach(input => {
                    const reqText = input.getAttribute('lay-reqtext');
                    if (reqText) {
                        if (reqText.includes('单品实际成交价') || reqText.includes('APP搜索页面展示价格')) {
                            input.value = data.price;
                            this.triggerLayuiEvents(input);
                        }
                    }
                });

                // 在当前fieldset中查找数量输入框
                const buyQuantityInput = fieldset.querySelector('.li_goods_buy input[type="number"]');
                if (buyQuantityInput) {
                    buyQuantityInput.value = "1";
                    this.triggerLayuiEvents(buyQuantityInput);
                }
            }, 300);
        }

        // 填充随机评论
        fillRandomComment() {
            try {
                // 获取所有商品组
                const taskGroups = document.querySelectorAll('li[name^="lstPicTask"]');
                console.log(`找到 ${taskGroups.length} 个商品组`);

                // 遍历每个商品组
                taskGroups.forEach((group, groupIndex) => {
                    // 为每个组生成一个随机评论，确保组内评论一致
                    const randomComment = this.commentManager.getRandomComment();
                    if (!randomComment) {
                        console.log(`第 ${groupIndex + 1} 组没有可用的评论`);
                        return;
                    }

                    // 查找主商品的评论框（图文报告内容）
                    const mainComment = group.querySelector('textarea[placeholder="请输入图文报告内容"]');
                    if (mainComment) {
                        mainComment.value = randomComment;
                        mainComment.dispatchEvent(new Event('input', { bubbles: true }));
                        mainComment.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`第 ${groupIndex + 1} 组主商品评论已填充`);
                    }

                    // 查找该组内所有子商品的评论框
                    const subComments = group.querySelectorAll('textarea[name^="item.CommentContent"]');
                    subComments.forEach((comment, subIndex) => {
                        comment.value = randomComment;
                        comment.dispatchEvent(new Event('input', { bubbles: true }));
                        comment.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`第 ${groupIndex + 1} 组第 ${subIndex + 1} 个子商品评论已填充`);
                    });
                });

                console.log('所有评论框填充完成');
            } catch (error) {
                console.error('填充评论时出错:', error);
            }
        }

        // 填充输入框（规格和关键词）
        fillInputs() {
            try {
                // 获取所有商品组
                const taskGroups = document.querySelectorAll('li[name^="lstPicTask"]');
                console.log(`找到 ${taskGroups.length} 个商品组`);

                // 遍历每个商品组
                taskGroups.forEach((group, groupIndex) => {
                    // 查找主商品的规格输入框
                    const mainSpec = group.querySelector('input[placeholder="请输入下单商品的规格"][lay-verify="required"]');
                    if (mainSpec) {
                        mainSpec.value = 'M';
                        mainSpec.dispatchEvent(new Event('input', { bubbles: true }));
                        mainSpec.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`第 ${groupIndex + 1} 组主商品规格已填充为 M`);
                    }

                    // 查找子商品的规格输入框，使用name属性模式
                    const subSpecs = group.querySelectorAll(`input[name^="item.GoodsAttr${groupIndex}_"]`);
                    subSpecs.forEach((spec, subIndex) => {
                        spec.value = 'M';
                        spec.dispatchEvent(new Event('input', { bubbles: true }));
                        spec.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`第 ${groupIndex + 1} 组第 ${subIndex + 1} 个子商品规格已填充为 M`);
                    });
                });

                // 单独处理效验浏览关键词输入框（不在组内）
                const keywordInput = document.querySelector('input[placeholder="请输入效验浏览关键词"][lay-verify="required|str"]');
                if (keywordInput) {
                    keywordInput.value = '价格说明';
                    keywordInput.dispatchEvent(new Event('input', { bubbles: true }));
                    keywordInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('效验浏览关键词已填充为: 价格说明');
                }

                console.log('所有输入框填充完成');
            } catch (error) {
                console.error('填充输入框时出错:', error);
            }
        }

        // 触发layui相关的所有事件
        triggerLayuiEvents(input) {
            this.triggerEvent(input, 'input');
            this.triggerEvent(input, 'propertychange');
            this.triggerEvent(input, 'change');
            
            setTimeout(() => {
                input.blur();
                if (window.layui && window.layui.form) {
                    window.layui.form.verify();
                }
            }, 50);
        }

        // 触发指定事件
        triggerEvent(element, eventType) {
            const event = new Event(eventType, {
                bubbles: true,
                cancelable: true,
                composed: true
            });
            element.dispatchEvent(event);
        }

        // 初始化
        async initialize() {
            if (this.initialized) return;
            
            console.log('开始初始化DataFiller...');

            try {
                // 等待页面加载完成
                if (document.readyState !== 'complete') {
                    await new Promise(resolve => window.addEventListener('load', resolve));
                }

                // 初始化评价管理器
                await this.commentManager.initialize();

                // 使用MutationObserver监听DOM变化
                const observer = new MutationObserver((mutations, obs) => {
                    const taskGroup = document.querySelector('li[name^="lstPicTask"]');
                    if (taskGroup) {
                        this.createFloatingButton();
                        obs.disconnect(); // 找到元素后停止观察
                    }
                });

                // 开始观察
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // 先检查一次元素是否已存在
                const taskGroup = document.querySelector('li[name^="lstPicTask"]');
                if (taskGroup) {
                    this.createFloatingButton();
                    observer.disconnect();
                }
 
                // 设置消息监听
                chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                    if (message.type === 'fillData') {
                        this.fillData(message.data);
                    }
                });

                this.initialized = true;
                console.log('DataFiller初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
            }
        }
    }

    // 添加一个控制面板管理器类
    class ControlPanelManager {
        constructor() {
            this.panel = null;
        }

        createControlPanel() {
            const panel = document.createElement('div');
            panel.id = 'upload-control-panel';
            panel.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px;
                background: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-radius: 4px;
                z-index: 9999;
                display: none;
            `;

            const stopButton = document.createElement('button');
            stopButton.id = 'stopUpload';
            stopButton.textContent = '终止上传';
            stopButton.style.cssText = `
                padding: 5px 15px;
                background: #FF4D4F;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            `;

            panel.appendChild(stopButton);
            document.body.appendChild(panel);
            this.panel = panel;

            // 添加点击事件
            stopButton.addEventListener('click', () => {
                const uploader = new ImageUploader();
                uploader.stopProcessing();
                this.hidePanel();
            });
        }

        showPanel() {
            if (!this.panel) {
                this.createControlPanel();
            }
            this.panel.style.display = 'block';
        }

        hidePanel() {
            if (this.panel) {
                this.panel.style.display = 'none';
            }
        }
    }

    // 辅助函数：分析页面结构，寻找可能的商品名称输入框
    function analyzePageStructure() {
        console.log('【页面分析】开始分析页面结构以找到商品名称输入框');
        
        // 查找所有输入框
        const allInputs = document.querySelectorAll('input[type="text"], input:not([type])');
        console.log(`【页面分析】找到 ${allInputs.length} 个文本输入框`);
        
        // 检查可能的商品名称输入框
        const possibleTitleInputs = [];
        
        allInputs.forEach((input, index) => {
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';
            const placeholder = input.getAttribute('placeholder') || '';
            const layVerify = input.getAttribute('lay-verify') || '';
            const parent = input.parentElement;
            const parentText = parent ? parent.textContent.trim() : '';
            
            // 可能的线索
            const isTitleInput = 
                name.includes('GoodsName') || 
                name.includes('goodsName') || 
                name.includes('Title') || 
                name.includes('title') ||
                id.includes('title') || 
                id.includes('name') ||
                placeholder.includes('商品') || 
                placeholder.includes('名称') || 
                placeholder.includes('标题') ||
                layVerify.includes('title') ||
                parentText.includes('名称') || 
                parentText.includes('标题') || 
                parentText.includes('商品');
                
            if (isTitleInput) {
                possibleTitleInputs.push({
                    index,
                    element: input,
                    name,
                    id,
                    placeholder,
                    layVerify,
                    parentText: parentText.substring(0, 50), // 截断过长的文本
                    value: input.value
                });
            }
        });
        
        console.log('【页面分析】可能的商品名称输入框:', possibleTitleInputs);
        
        // 基于分析结果推荐选择器
        if (possibleTitleInputs.length > 0) {
            const recommendations = possibleTitleInputs.map(input => {
                let selector = '';
                if (input.id) selector += `#${input.id}`;
                else if (input.name) selector += `input[name="${input.name}"]`;
                else if (input.placeholder) selector += `input[placeholder="${input.placeholder}"]`;
                else if (input.layVerify) selector += `input[lay-verify*="${input.layVerify}"]`;
                return selector;
            }).filter(s => s);
            
            console.log('【页面分析】推荐的选择器:', recommendations.join(', '));
        } else {
            console.log('【页面分析】无法找到可能的商品名称输入框');
        }
    }

    // 直接测试按钮点击功能
    function testButtonClick() {
        console.log('【直接测试】开始执行直接的按钮点击测试');
        
        const button = document.querySelector('#ai-analysis-button');
        if (!button) {
            console.log('【直接测试】找不到AI分析按钮，无法测试点击');
            return;
        }
        
        console.log('【直接测试】找到按钮:', button);
        
        try {
            // 测试按钮属性
            console.log('【直接测试】按钮类型:', button.type);
            console.log('【直接测试】按钮disabled状态:', button.disabled);
            console.log('【直接测试】按钮parent节点:', button.parentElement);
            
            // 查看按钮内联onclick
            console.log('【直接测试】按钮内联onclick:', button.getAttribute('onclick'));
            console.log('【直接测试】按钮js onclick:', typeof button.onclick);
            
            // 手动调用handleAnalysisButtonClick函数
            console.log('【直接测试】尝试直接调用全局函数');
            
            window.tempAIManager = new AIFeatureManager();
            window.tempAIManager.handleAnalysisButtonClick();
            
            console.log('【直接测试】测试完成');
        } catch (error) {
            console.error('【直接测试】测试失败:', error);
        }
    }

    // 创建全局强制测试按钮
    function createForceTestButton() {
        // 如果已经存在，则不重复创建
        if (document.getElementById('force-ai-test-button')) {
            return;
        }
        
        // 获取AI设置
        const aiSettings = getAISettings();
        
        // 创建主容器
        const container = document.createElement('div');
        container.id = 'force-ai-test-container';
        container.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            width: 220px;
            padding: 15px;
            background: white;
            border: 2px solid #1E9FFF;
            border-radius: 5px;
            z-index: 9999999;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        `;
        
        // 创建标题
        const title = document.createElement('div');
        title.style.cssText = `
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
            text-align: center;
        `;
        title.textContent = 'AI功能测试';
        container.appendChild(title);
        
        // 创建模型选择下拉框
        const modelLabel = document.createElement('div');
        modelLabel.textContent = '选择模型:';
        modelLabel.style.cssText = `margin-bottom: 5px; font-size: 14px;`;
        container.appendChild(modelLabel);
        
        const modelSelect = document.createElement('select');
        modelSelect.id = 'ai-model-select';
        modelSelect.style.cssText = `
            width: 100%;
            padding: 5px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        `;
        
        // 添加支持的模型选项
        const modelOptions = [
            ['Qwen/QwQ-32B-Preview', 'QwQ-32B (推荐)'],
            ['Qwen/QwQ-Max', 'QwQ-Max'],
            ['Qwen/Qwen2-72B-Instruct', 'Qwen2-72B'],
            ['Qwen/Qwen1.5-110B-Chat', 'Qwen1.5-110B'],
            ['Qwen/Qwen1.5-72B-Chat', 'Qwen1.5-72B'],
            ['Qwen/Qwen1.5-32B-Chat', 'Qwen1.5-32B'],
            ['Qwen/Qwen1.5-14B-Chat', 'Qwen1.5-14B'],
            ['Qwen/Qwen1.5-7B-Chat', 'Qwen1.5-7B'],
            ['anthropic/claude-3-5-sonnet', 'Claude-3.5-Sonnet'],
            ['anthropic/claude-3-opus', 'Claude-3-Opus'],
            ['anthropic/claude-3-sonnet', 'Claude-3-Sonnet'],
            ['anthropic/claude-3-haiku', 'Claude-3-Haiku'],
            ['deepseek/deepseek-chat', 'DeepSeek-Chat'],
            ['meta/llama-3-70b-instruct', 'Llama-3-70B'],
            ['meta/llama-3-8b-instruct', 'Llama-3-8B']
        ];
        
        modelOptions.forEach(([value, text]) => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            if (value === aiSettings.model) {
                option.selected = true;
            }
            modelSelect.appendChild(option);
        });
        
        container.appendChild(modelSelect);
        
        // 创建API键输入框
        const apiKeyLabel = document.createElement('div');
        apiKeyLabel.textContent = 'API Key:';
        apiKeyLabel.style.cssText = `margin-bottom: 5px; font-size: 14px;`;
        container.appendChild(apiKeyLabel);
        
        const apiKeyInput = document.createElement('input');
        apiKeyInput.id = 'ai-api-key';
        apiKeyInput.type = 'text';
        apiKeyInput.value = aiSettings.apiKey;
        apiKeyInput.placeholder = '输入你的API Key';
        apiKeyInput.style.cssText = `
            width: 100%;
            padding: 5px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        `;
        container.appendChild(apiKeyInput);
        
        // 创建API URL输入框
        const apiUrlLabel = document.createElement('div');
        apiUrlLabel.textContent = 'API URL:';
        apiUrlLabel.style.cssText = `margin-bottom: 5px; font-size: 14px;`;
        container.appendChild(apiUrlLabel);
        
        const apiUrlInput = document.createElement('input');
        apiUrlInput.id = 'ai-api-url';
        apiUrlInput.type = 'text';
        apiUrlInput.value = aiSettings.apiUrl;
        apiUrlInput.placeholder = 'API URL';
        apiUrlInput.style.cssText = `
            width: 100%;
            padding: 5px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        `;
        container.appendChild(apiUrlInput);
        
        // 创建保存设置按钮
        const saveButton = document.createElement('button');
        saveButton.id = 'save-ai-settings';
        saveButton.textContent = '保存设置';
        saveButton.style.cssText = `
            padding: 5px 12px;
            background: #009688;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 5px;
            margin-bottom: 15px;
        `;
        
        // 保存设置事件
        saveButton.onclick = function() {
            const model = document.getElementById('ai-model-select').value;
            const apiKey = document.getElementById('ai-api-key').value;
            const apiUrl = document.getElementById('ai-api-url').value;
            
            // 验证输入
            if (!model || !apiKey || !apiUrl) {
                alert('请填写所有字段');
                return;
            }
            
            // 保存设置
            const settings = { model, apiKey, apiUrl };
            if (saveAISettings(settings)) {
                // 更新状态
                document.getElementById('force-ai-test-status').textContent = '状态: 设置已保存';
                document.getElementById('force-ai-test-status').style.color = 'green';
                
                // 2秒后恢复状态
                setTimeout(() => {
                    const statusDiv = document.getElementById('force-ai-test-status');
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 等待点击';
                        statusDiv.style.color = '#666';
                    }
                }, 2000);
            }
        };
        
        container.appendChild(saveButton);
        
        // 创建测试按钮
        const button = document.createElement('button');
        button.id = 'force-ai-test-button';
        button.textContent = '执行';
        button.style.cssText = `
            padding: 8px 16px;
            background: #1E9FFF;
            color: white;
            font-size: 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            display: block; /* 确保按钮显示 */
        `;
        
        // 按钮点击事件
        button.onclick = function() {
            directAnalyzeTitle();
        };
        
        container.appendChild(button);
        
        // 创建状态显示
        const status = document.createElement('div');
        status.id = 'force-ai-test-status';
        status.style.cssText = `
            margin-top: 10px;
            font-size: 14px;
            color: #666;
            text-align: center;
        `;
        status.textContent = '状态: 等待点击';
        container.appendChild(status);
        
        // 添加到文档
        document.body.appendChild(container);
        
        console.log('创建了AI测试按钮');
    }

    // 修改初始化函数
    function initializeInterface() {
        console.log('【调试】开始初始化界面');
        
        // 创建强制测试按钮
        setTimeout(() => {
            createForceTestButton();
        }, 2000);
        
        // 页面加载后分析页面结构
        setTimeout(() => {
            console.log('【调试】执行页面结构分析');
            analyzePageStructure();
        }, 1000);
        
        // 初始化图片上传器
        const uploader = new ImageUploader();
        uploader.initializeGroupBackgrounds();

        // 初始化粘贴功能
        const pasteManager = new PasteAreaManager();
        pasteManager.initialize();

        // 初始化数据填充器
        const dataFiller = new DataFiller();
        dataFiller.initialize();
    }

    // 确保在页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeInterface);
    } else {
        initializeInterface();
    }

    // 监听消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'startProcessing') {
            console.log('开始处理图片组，共', message.reviews.length, '组');
            
            sendResponse({ received: true });
            
            const uploader = new ImageUploader();
            uploader.processAllGroups(message.reviews)
                .then(result => {
                    console.log('处理完成:', result);
                })
                .catch(error => {
                    console.error('处理失败:', error);
                });
            
            return true;
        } else if (message.type === 'stopProcessing') {
            const uploader = new ImageUploader();
            uploader.stopProcessing();
            sendResponse({ received: true });
        }
        return true;
    });

    // 获取AI设置的函数
    function getAISettings() {
        // 从localStorage获取设置，如果没有则使用默认值
        const savedSettings = localStorage.getItem('aiAnalysisSettings');
        if (savedSettings) {
            try {
                return JSON.parse(savedSettings);
            } catch (error) {
                console.error('解析保存的AI设置失败:', error);
            }
        }
        
        // 默认设置
        return {
            apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
            model: 'Qwen/QwQ-32B-Preview',
            apiKey: 'sk-rgjclhvsomldsqiazlccwuwoasboxikminjrvtlblnlsfuvb'
        };
    }
    
    // 保存AI设置的函数
    function saveAISettings(settings) {
        try {
            localStorage.setItem('aiAnalysisSettings', JSON.stringify(settings));
            console.log('AI设置已保存');
            return true;
        } catch (error) {
            console.error('保存AI设置失败:', error);
            return false;
        }
    }
    
    // 直接简化版分析标题函数，绕过所有可能的问题
    function directAnalyzeTitle() {
        console.log('【直接分析】开始直接分析标题');
        
        try {
            // 1. 获取标题
            const titleInputs = document.querySelectorAll('input[name="item.GoodsName"]');
            if (!titleInputs || titleInputs.length === 0) {
                console.log('【直接分析】找不到标题输入框');
                return;
            }
            
            const title = titleInputs[0].value.trim();
            if (!title) {
                console.log('【直接分析】标题为空');
                return;
            }
            
            console.log('【直接分析】获取到标题:', title);
            
            // 2. 创建结果容器（如果不存在）
            let resultContainer = document.getElementById('direct-ai-result');
            if (!resultContainer) {
                resultContainer = document.createElement('div');
                resultContainer.id = 'direct-ai-result';
                resultContainer.style.cssText = `
                    position: fixed;
                    top: 150px;
                    right: 50px;
                    width: 300px;
                    padding: 15px;
                    background: white;
                    border: 2px solid #1E9FFF;
                    border-radius: 5px;
                    z-index: 9999999;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
                    max-height: 400px;
                    overflow-y: auto;
                `;
                document.body.appendChild(resultContainer);
            }
            
            // 3. 显示加载状态
            resultContainer.innerHTML = '<div style="text-align:center;padding:20px;">正在分析标题，请稍候...</div>';
            resultContainer.style.display = 'block';
            
            // 4. 获取任务数量
            let taskCount = 1; // 默认值
            
            // 首先通过lstPicTask元素计算
            const taskContainers = document.querySelectorAll('li[name^="lstPicTask"]');
            if (taskContainers && taskContainers.length > 0) {
                taskCount = taskContainers.length;
                console.log(`【直接分析】通过lstPicTask元素检测到任务数量: ${taskCount}`);
            } else {
                // 通过"搜索关键词"标签数量计算
                const searchLabels = Array.from(document.querySelectorAll('label')).filter(label => 
                    label.textContent.trim().includes('搜索关键词') || 
                    label.textContent.trim().includes('搜索词')
                );
                
                if (searchLabels && searchLabels.length > 0) {
                    taskCount = searchLabels.length;
                    console.log(`【直接分析】通过"搜索关键词"标签检测到任务数量: ${taskCount}`);
                } else {
                    // 备用方法：通过页面文本
                    const textElements = document.querySelectorAll('.layui-elem-field legend, .strong');
                    for (const element of textElements) {
                        const match = element.textContent.match(/第(\d+)单/);
                        if (match && match[1]) {
                            const count = parseInt(match[1]);
                            if (!isNaN(count) && count > 0) {
                                taskCount = count; 
                                console.log(`【直接分析】通过页面文本找到任务数量: ${taskCount}`);
                                break;
                            }
                        }
                    }
                }
            }
            
            console.log(`【直接分析】最终使用的任务数量: ${taskCount}`);
            
            // 5. 获取API配置
            const aiSettings = getAISettings();
            
            // 6. 调用API
            console.log('【直接分析】准备调用API');
            console.log('【直接分析】使用模型:', aiSettings.model);
            
            // 构造请求参数 - 使用与AIFeatureManager相同的提示词模板
            const prompt = AI_CONFIG.PROMPT_TEMPLATE
                .replace('{title}', title)
                .replace(/\{count\}/g, taskCount);
            
            console.log('【直接分析】使用的提示词:', prompt);
            
            // 更新状态
            const statusDiv = document.getElementById('force-ai-test-status');
            if (statusDiv) {
                statusDiv.textContent = '状态: 分析中...';
                statusDiv.style.color = '#1E9FFF';
            }
            
            // 发送请求
            fetch(aiSettings.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${aiSettings.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: aiSettings.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: 1024,
                    temperature: 0.5,
                    top_p: 0.8
                })
            })
            .then(response => {
                console.log('【直接分析】API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('【直接分析】API返回数据:', data);
                
                if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                    const content = data.choices[0].message.content.trim();
                    console.log('【直接分析】解析到的内容:', content);
                    
                    // 尝试解析JSON
                    let keywords = [];
                    try {
                        // 查找JSON数组部分
                        let jsonText = content;
                        // 如果回答中包含额外文本，尝试提取出JSON部分
                        const jsonMatch = content.match(/\[\s*"[^"]*"(?:\s*,\s*"[^"]*")*\s*\]/);
                        if (jsonMatch) {
                            jsonText = jsonMatch[0];
                        }
                        
                        const parsedKeywords = JSON.parse(jsonText);
                        if (Array.isArray(parsedKeywords) && parsedKeywords.length > 0) {
                            keywords = parsedKeywords;
                        }
                    } catch (jsonError) {
                        console.log('【直接分析】JSON解析失败，尝试使用传统方法解析:', jsonError);
                        // 回退到传统的分割方法
                        keywords = content.split(/[,，]/).map(k => k.trim()).filter(k => k);
                    }
                    
                    console.log('【直接分析】最终关键词列表:', keywords);
                    
                    // 直接应用关键词到搜索框
                    applyDirectKeywords(keywords);
                    
                    // 显示结果，但不显示应用按钮
                    resultContainer.innerHTML = `
                        <h3 style="margin:0 0 10px 0;color:#333;border-bottom:1px solid #eee;padding-bottom:8px;">🔍 AI推荐关键词 (${keywords.length}个)</h3>
                        <div style="margin:10px 0;font-weight:bold;color:#1E9FFF;">${keywords.join('，')}</div>
                        <div style="margin-top:15px;">
                            <button id="direct-copy-btn" style="padding:5px 12px;background:#009688;color:white;border:none;border-radius:3px;cursor:pointer;">复制全部</button>
                            <button id="direct-close-btn" style="padding:5px 12px;margin-left:5px;background:#FF5722;color:white;border:none;border-radius:3px;cursor:pointer;">关闭</button>
                        </div>
                    `;
                    
                    // 添加复制按钮事件
                    document.getElementById('direct-copy-btn').onclick = function() {
                        console.log('【直接分析】复制按钮被点击');
                        navigator.clipboard.writeText(keywords.join('，'))
                            .then(() => console.log('关键词已复制到剪贴板'))
                            .catch(err => console.error('复制失败:', err));
                    };
                    
                    // 添加关闭按钮事件
                    document.getElementById('direct-close-btn').onclick = function() {
                        resultContainer.style.display = 'none';
                    };
                    
                    // 更新测试状态
                    const statusDiv = document.getElementById('force-ai-test-status');
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 完成';
                        statusDiv.style.color = 'green';
                    }
                } else {
                    resultContainer.innerHTML = '<div style="color:red;padding:20px;">无法获取关键词</div>';
                    console.log('【直接分析】无法从API返回中解析关键词');
                    
                    // 更新测试状态
                    const statusDiv = document.getElementById('force-ai-test-status');
                    if (statusDiv) {
                        statusDiv.textContent = '状态: 分析失败';
                        statusDiv.style.color = 'red';
                    }
                }
            })
            .catch(error => {
                console.error('【直接分析】请求失败:', error);
                resultContainer.innerHTML = `<div style="color:red;padding:20px;">分析失败: ${error.message}</div>`;
                
                // 更新测试状态
                const statusDiv = document.getElementById('force-ai-test-status');
                if (statusDiv) {
                    statusDiv.textContent = '状态: 分析失败';
                    statusDiv.style.color = 'red';
                }
            });
            
        } catch (error) {
            console.error('【直接分析】执行过程中出错:', error);
        }
    }
    
    // 应用直接生成的关键词
    function applyDirectKeywords(keywords) {
        console.log('【直接分析】开始应用关键词到搜索框');
        
        try {
            // 使用更精确的选择器查找搜索关键词输入框
            let keywordInputs = [];
            
            // 方法1：通过标签文本和输入框特征精确查找
            const labelElements = Array.from(document.querySelectorAll('label'));
            const searchLabels = labelElements.filter(label => 
                label.textContent.trim().includes('搜索关键词') || 
                label.textContent.trim().includes('搜索词')
            );
            
            console.log(`【直接分析】找到 ${searchLabels.length} 个搜索关键词标签`);
            
            for (const label of searchLabels) {
                // 查找标签后的输入框容器
                const formItem = label.closest('.layui-form-item');
                if (formItem) {
                    // 在这个表单项中查找输入框
                    const qrBox = formItem.querySelector('.layui-input-inline.qrbox');
                    if (qrBox) {
                        const inputs = qrBox.querySelectorAll('input[type="text"]');
                        if (inputs && inputs.length > 0) {
                            inputs.forEach(input => keywordInputs.push(input));
                            console.log(`【直接分析】通过标签和qrbox找到 ${inputs.length} 个输入框`);
                        }
                    } else {
                        // 如果没有qrbox，尝试直接查找输入框
                        const inputs = formItem.querySelectorAll('input[type="text"][placeholder*="搜索关键词"]');
                        if (inputs && inputs.length > 0) {
                            inputs.forEach(input => keywordInputs.push(input));
                            console.log(`【直接分析】通过标签和placeholder找到 ${inputs.length} 个输入框`);
                        }
                    }
                }
            }
            
            // 如果上面的方法没找到，尝试直接通过placeholder查找
            if (keywordInputs.length === 0) {
                const placeholderInputs = document.querySelectorAll('input[placeholder*="搜索关键词"]');
                if (placeholderInputs && placeholderInputs.length > 0) {
                    placeholderInputs.forEach(input => keywordInputs.push(input));
                    console.log(`【直接分析】通过placeholder找到 ${placeholderInputs.length} 个输入框`);
                }
            }
            
            // 如果还是没找到，尝试第三种方法：通过父元素的类名查找
            if (keywordInputs.length === 0) {
                const qrBoxes = document.querySelectorAll('.layui-input-inline.qrbox');
                for (const box of qrBoxes) {
                    const inputs = box.querySelectorAll('input[type="text"]');
                    if (inputs && inputs.length > 0) {
                        inputs.forEach(input => keywordInputs.push(input));
                        console.log(`【直接分析】通过qrbox找到 ${inputs.length} 个输入框`);
                    }
                }
            }
            
            // 最后一种方法：找到所有的lstPicTask元素，然后在其中查找与"搜索关键词"相关的输入框
            if (keywordInputs.length === 0) {
                const taskContainers = document.querySelectorAll('li[name^="lstPicTask"]');
                for (const container of taskContainers) {
                    // 在每个任务容器中寻找包含"搜索关键词"文本的标签
                    const taskLabels = Array.from(container.querySelectorAll('label')).filter(label => 
                        label.textContent.trim().includes('搜索关键词')
                    );
                    
                    for (const label of taskLabels) {
                        const formItem = label.closest('.layui-form-item');
                        if (formItem) {
                            const inputs = formItem.querySelectorAll('input[type="text"]');
                            if (inputs && inputs.length > 0) {
                                inputs.forEach(input => keywordInputs.push(input));
                                console.log(`【直接分析】通过任务容器找到 ${inputs.length} 个输入框`);
                            }
                        }
                    }
                }
            }
            
            if (keywordInputs.length === 0) {
                console.log('【直接分析】找不到关键词输入框');
                return;
            }
            
            console.log('【直接分析】最终找到的关键词输入框数量:', keywordInputs.length);
            
            // 填充数据
            const maxKeywords = Math.min(keywords.length, keywordInputs.length);
            
            for (let i = 0; i < maxKeywords; i++) {
                const input = keywordInputs[i];
                input.value = keywords[i];
                console.log(`【直接分析】填充关键词 ${i+1}: "${keywords[i]}" 到元素:`, input);
                
                // 触发事件
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
            console.log(`【直接分析】已将 ${maxKeywords} 个关键词应用到搜索框`);
        } catch (error) {
            console.error('【直接分析】应用关键词失败:', error);
        }
    }
})();