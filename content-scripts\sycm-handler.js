/**
 * 处理网站A页面上与生意参谋跳转相关的功能
 */
window.SycmHandler = class SycmHandler {
    constructor() {
        this.observer = null;
        this.throttledAddButtons = Utils.throttle(this.addSycmJumpButtons.bind(this), 1000); // 1秒节流
    }

    /**
     * 初始化，开始监听URL输入变化
     */
    initialize() {
        Utils.log('SYCM处理', '初始化生意参谋处理器');
        this.setupUrlObserver();
        // 初始添加一次按钮
        this.addSycmJumpButtons();
    }

    /**
     * 设置MutationObserver来监听输入框值的变化
     */
    setupUrlObserver() {
        if (this.observer) {
            this.observer.disconnect(); // 先断开旧的观察者
        }

        const targetNode = document.body;
        const config = { attributes: true, childList: true, subtree: true, attributeFilter: ['value'] };

        const callback = (mutationsList, observer) => {
            let needsUpdate = false;
            for (const mutation of mutationsList) {
                // 主要关注 value 属性变化
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    // 检查目标元素是否是文本输入框
                    if (mutation.target.tagName === 'INPUT' && mutation.target.type === 'text') {
                        needsUpdate = true;
                        break;
                    }
                } else if (mutation.type === 'childList') {
                    // 如果有节点添加或删除，也可能需要更新（例如动态添加的输入框）
                    needsUpdate = true;
                    break;
                }
            }
            if (needsUpdate) {
                // 使用节流函数来避免过于频繁的调用
                this.throttledAddButtons();
            }
        };

        this.observer = new MutationObserver(callback);
        this.observer.observe(targetNode, config);

        Utils.log('SYCM处理', 'URL输入观察者已启动');
    }

    /**
     * 提取淘宝商品ID (从Utils中获取，确保一致性)
     * @param {string} url - 淘宝商品URL
     * @returns {string|null} 商品ID或null
     */
    _extractTaobaoItemId(url) {
        // 直接调用Utils中的方法
        return Utils.extractTaobaoItemId(url);
    }

    /**
     * 生成生意参谋需要的日期范围参数
     * @returns {string} 日期范围参数 YYYY-MM-DD%7CYYYY-MM-DD 格式
     * @private
     */
    _getRecentDateRange() {
        try {
            const today = new Date();
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(today.getDate() - 7);

            const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            const dateRange = `${formatDate(sevenDaysAgo)}%7C${formatDate(today)}`;
            Utils.log('SYCM处理', `生成日期范围参数: ${dateRange}`);
            return dateRange;
        } catch (error) {
            Utils.log('SYCM处理', `生成日期范围参数出错: ${error.message}`, 'error');
            return 'dateRange=recent7'; // Fallback
        }
    }

    /**
     * 生成生意参谋跳转链接
     * @param {string} itemId - 商品ID
     * @returns {string} 完整的生意参谋URL
     * @private
     */
    _generateSycmUrl(itemId) {
        if (!itemId) return '';
        try {
            const dateRange = this._getRecentDateRange();
            // 使用AppConfig中的模板或默认值
            const baseUrl = AppConfig.SITES.SITE_A.SYCM_BASE_URL || 'https://sycm.taobao.com/cc/item_archives';
            return `${baseUrl}?activeKey=title&activeTab=search&dateRange=${dateRange}&dateType=recent7&itemId=${itemId}`;
        } catch (error) {
            Utils.log('SYCM处理', `生成生意参谋URL出错: ${error.message}`, 'error');
            // Fallback URL
            return `https://sycm.taobao.com/cc/item_archives?activeKey=title&activeTab=search&dateType=recent7&itemId=${itemId}`;
        }
    }

    /**
     * 添加生意参谋跳转按钮
     */
    addSycmJumpButtons() {
        try {
            // Utils.log('SYCM处理', '尝试添加/更新生意参谋跳转按钮');

            const allTextInputs = Utils.findAllElements('input[type="text"]', document); // 使用Utils查找
            let addedCount = 0;
            let updatedCount = 0;

            for (const input of allTextInputs) {
                const value = input.value || '';
                const nextElement = input.nextElementSibling;
                const existingButton = nextElement && nextElement.classList.contains('sycm-jump-btn') ? nextElement : null;

                // 检查是否是淘宝链接
                if (value && (value.includes('taobao.com') || value.includes('tmall.com') || value.includes('item.htm'))) {
                    const itemId = this._extractTaobaoItemId(value);

                    if (itemId) {
                        const sycmUrl = this._generateSycmUrl(itemId);

                        if (existingButton) {
                            // 如果按钮已存在，检查URL是否需要更新
                            if (existingButton.href !== sycmUrl) {
                                existingButton.href = sycmUrl;
                                updatedCount++;
                                Utils.log('SYCM处理', `更新商品ID ${itemId} 的生意参谋按钮链接`);
                            }
                        } else {
                            // 如果按钮不存在，创建并添加
                            const buttonStyle = AppConfig.STYLES.SYCM_BUTTON || `
                                margin-left: 5px;
                                padding: 2px 10px;
                                background: #FF5000;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 12px;
                                height: ${input.offsetHeight}px; /* 尝试匹配输入框高度 */
                                line-height: ${input.offsetHeight}px;
                                white-space: nowrap;
                                display: inline-block;
                                vertical-align: middle;
                                text-decoration: none;
                            `;

                            const jumpButton = Utils.createElement('a', {
                                href: sycmUrl,
                                target: '_blank',
                                class: 'sycm-jump-btn',
                                style: buttonStyle,
                                title: '点击查看该商品的生意参谋数据'
                            }, '生意参谋');

                            const parent = input.parentNode;
                            if (parent) {
                                parent.insertBefore(jumpButton, input.nextSibling);
                                addedCount++;
                                Utils.log('SYCM处理', `为商品ID ${itemId} 添加了生意参谋跳转按钮`);
                            }
                        }
                    } else {
                        // 如果当前值是淘宝链接但无法提取ID，且存在按钮，则移除按钮
                        if (existingButton) {
                            existingButton.remove();
                             Utils.log('SYCM处理', '因无法提取ID而移除旧的生意参谋按钮');
                        }
                    }
                } else {
                    // 如果当前值不是淘宝链接，且存在按钮，则移除按钮
                    if (existingButton) {
                        existingButton.remove();
                        Utils.log('SYCM处理', '因输入框内容不再是淘宝链接而移除生意参谋按钮');
                    }
                }
            }

            if (addedCount > 0) {
                Utils.log('SYCM处理', `共添加了 ${addedCount} 个新的生意参谋跳转按钮`);
                // 可以在这里调用全局通知，如果需要的话
                // Utils.showGlobalNotification(`成功添加 ${addedCount} 个生意参谋跳转按钮`, 'success', 2000);
            }
            // if (updatedCount > 0) {
            //     Utils.log('SYCM处理', `共更新了 ${updatedCount} 个生意参谋跳转按钮链接`);
            // }

        } catch (error) {
            Utils.log('SYCM处理', `添加/更新生意参谋跳转按钮出错: ${error.message}`, 'error');
            // 可以考虑添加错误提示
        }
    }
}; 