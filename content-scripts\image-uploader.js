/**
 * 图片上传模块
 * 负责图片处理、压缩和上传功能
 */

// 图片上传器类 - 整合了 site-a-content.js 中的逻辑
window.ImageUploader = class ImageUploader {
    constructor() {
        this.processing = false;
        this.groups = []; // 存储待处理的图片组 [{ images: [url1, url2...] }, ...]
        this.currentGroupIndex = 0;
        this.MAX_RETRIES = AppConfig.UPLOAD.MAX_RETRIES || 3;
        this.retryDelay = AppConfig.UPLOAD.RETRY_DELAY || 500;
        this.uploadedImages = new Set(); // 存储已上传的图片 URL 或标识
        this.shouldStop = false;
        this.groupColors = AppConfig.STYLES.GROUP_COLORS || [
            '#f9f9ff', '#f9fff9', '#fff9f9', '#f9ffff', '#fffff9'
        ];
        // 关联 ControlPanelManager 以更新状态
        // this.controlPanel = new ControlPanelManager(); // 依赖注入或全局实例可能更好
    }
    
    /**
     * 初始化上传器 (主要用于设置背景色等视觉效果)
     */
    initialize() {
        Utils.log('图片上传', '初始化图片上传器');
        this.initializeGroupBackgrounds();
    }
    
    /**
     * 初始化图片组背景色 (针对网站A的特定结构)
     */
    initializeGroupBackgrounds() {
        Utils.log('图片上传', '初始化图片组背景 (网站A)');
        try {
            // 使用 AppConfig 中的选择器
            const mainGroups = document.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
            if (mainGroups.length > 0) {
                 mainGroups.forEach((group, index) => {
                    this.setGroupBackground(group, index);
                 });
                 Utils.log('图片上传', `设置了 ${mainGroups.length} 个图片组的背景色`);
            } else {
                Utils.log('图片上传', '未找到网站A的任务容器来设置背景色', 'warn');
                return; // 找不到就不设置
            }

            // 监听DOM变化，为新添加的组添加背景色 (可选，可能影响性能)
            // const observer = new MutationObserver((mutations) => {
            //     mutations.forEach(() => {
            //         const groups = document.querySelectorAll(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER);
            //         groups.forEach((group, index) => {
            //             // 避免重复设置样式
            //             if (!group.style.backgroundColor) {
            //                  this.setGroupBackground(group, index);
            //             }
            //         });
            //     });
            // });
            // observer.observe(document.body, { childList: true, subtree: true });
            
        } catch (error) {
            console.error('设置图片组背景色失败:', error);
        }
    }
    
    /**
     * 为单个图片组设置背景色
     * @param {Element} mainGroup - 任务容器元素
     * @param {number} index - 索引
     */
    setGroupBackground(mainGroup, index) {
        const colorIndex = index % this.groupColors.length;
        mainGroup.style.backgroundColor = this.groupColors[colorIndex];
        // 可以考虑添加其他样式，如 padding, border-radius
        mainGroup.style.padding = AppConfig.STYLES.GROUP_PADDING || '10px';
        mainGroup.style.borderRadius = AppConfig.STYLES.GROUP_BORDER_RADIUS || '5px';
        mainGroup.style.marginBottom = AppConfig.STYLES.GROUP_MARGIN_BOTTOM || '10px';
    }
    
    /**
     * 处理所有图片组 (由 background.js 调用)
     * @param {Array} reviews - 要处理的评论组 [{ images: [url1, url2...] }, ...]
     * @returns {Promise<Object>} - 处理结果 { success: boolean, results?: Array, message?: string }
     */
    async processAllGroups(reviews) {
        if (this.processing) {
            Utils.log('图片上传', '已有处理任务在进行中，请等待完成', 'warn');
            return { success: false, message: '已有任务在进行中' };
        }
        
        if (!reviews || reviews.length === 0) {
            Utils.log('图片上传', '没有需要处理的图片组', 'warn');
            return { success: false, message: '没有图片组数据' };
        }
        
        this.processing = true;
        this.shouldStop = false; // 重置停止标志
        this.groups = reviews;
        this.currentGroupIndex = 0;
        this.uploadedImages.clear(); // 开始新任务前清空已上传记录
        
        Utils.log('图片上传', `开始处理 ${this.groups.length} 个图片组`);
        
        // 更新UI状态：显示处理中
        this.updateProcessingStatus(0, this.groups.length, '开始处理');
        
        try {
            const results = [];
            
            for (let i = 0; i < this.groups.length; i++) {
                if (this.shouldStop) {
                    Utils.log('图片上传', '处理被用户中断');
                    this.updateProcessingStatus(i, this.groups.length, '已中断');
                    break;
                }
                
                this.currentGroupIndex = i;
                const group = this.groups[i];
                const groupNumber = i + 1;
                
                Utils.log('图片上传', `--- 处理图片组 ${groupNumber}/${this.groups.length} ---`);
                
                // 更新UI状态：显示当前处理组
                this.updateProcessingStatus(groupNumber, this.groups.length, `处理组 ${groupNumber}`);
                
                // 确保活动已选择 (针对网站A)
                await this.ensureActivitySelected();
                
                // 处理单个图片组
                const result = await this.processSingleGroup(group, groupNumber);
                results.push(result);
                
                // 如果处理失败且非用户中断，可以选择停止或继续
                if (!result.success && !this.shouldStop) {
                    Utils.log('图片上传', `图片组 ${groupNumber} 处理失败，可能跳过后续组`, 'error');
                    // 根据配置决定是否中断
                    if (AppConfig.UPLOAD.STOP_ON_ERROR) {
                         this.updateProcessingStatus(groupNumber, this.groups.length, `组 ${groupNumber} 失败，已停止`);
                         break; 
                    }
                }
                
                // 等待一段时间后继续处理下一组，避免过快操作
                await Utils.delay(AppConfig.UPLOAD.GROUP_DELAY || 1000);
            }
            
            // 处理完成或中断后的最终状态
            if (!this.shouldStop) {
                Utils.log('图片上传', '所有图片组处理完成');
                this.updateProcessingStatus(this.groups.length, this.groups.length, '全部完成');
            } else {
                 Utils.log('图片上传', '处理任务已停止');
                 // 状态已在中断时更新
            }
            
            return {
                success: !this.shouldStop, // 仅当未被中断时算成功
                results: results
            };
            
        } catch (error) {
            console.error('处理图片组时发生严重错误:', error);
            Utils.log('图片上传', `处理过程中出错: ${error.message}`, 'error');
            this.updateProcessingStatus(this.currentGroupIndex + 1, this.groups.length, '处理出错');
            return {
                success: false,
                message: '处理过程中出错: ' + error.message
            };
        } finally {
            this.processing = false; // 确保处理状态被重置
            // 可以在此隐藏处理状态显示，或保留最终状态
            // setTimeout(() => this.updateProcessingStatus(0, 0, ''), 5000); // 5秒后清除状态
        }
    }
    
    /**
     * 处理单个图片组
     * @param {Object} group - 图片组数据 { images: [url1, url2...] }
     * @param {number} groupNumber - 当前组的序号 (从1开始)
     * @returns {Promise<Object>} - 处理结果 { success: boolean, uploaded?: Array, message?: string }
     */
    async processSingleGroup(group, groupNumber) {
        try {
            // 检查图片组数据
            if (!group || !group.images || group.images.length === 0) {
                Utils.log('图片上传', `图片组 ${groupNumber} 数据无效`, 'warn');
                return { success: false, message: `组 ${groupNumber} 数据无效` };
            }
            
            Utils.log('图片上传', `组 ${groupNumber}: 共 ${group.images.length} 张图片`);
            
            // 添加新的任务组 (网站A特定操作)
            const uploadArea = await this.addNewGroup(groupNumber);
            if (!uploadArea) {
                Utils.log('图片上传', `组 ${groupNumber}: 添加新任务组失败`, 'error');
                return { success: false, message: `组 ${groupNumber}: 添加新组失败` };
            }
            
            // 逐个处理图片
            const uploadedResults = [];
            for (let i = 0; i < group.images.length; i++) {
                 if (this.shouldStop) break; // 检查是否需要中断
                 
                 const imageUrl = group.images[i];
                 const imageNumber = i + 1;
                 
                 this.updateProcessingStatus(groupNumber, this.groups.length, `处理组 ${groupNumber} - 图片 ${imageNumber}/${group.images.length}`);
                 
                 // 处理单张图片，传入上传区域
                 const result = await this.processSingleImage(imageUrl, uploadArea, groupNumber, imageNumber);
                 uploadedResults.push(result);
                 
                 // 如果单张图片上传失败，根据配置决定是否继续
                 if (!result.success && AppConfig.UPLOAD.STOP_ON_ERROR) {
                     Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber} 上传失败，停止当前组`, 'error');
                     break; // 停止处理当前组的剩余图片
                 }
                 
                 // 短暂延迟，避免请求过于频繁
                 await Utils.delay(AppConfig.UPLOAD.IMAGE_DELAY || 300);
            }
            
            // 检查当前组是否所有图片都成功上传
            const groupSuccess = uploadedResults.every(r => r.success);
            Utils.log('图片上传', `组 ${groupNumber} 处理完成，状态: ${groupSuccess ? '成功' : '部分失败或中断'}`);
            
            return {
                success: groupSuccess && !this.shouldStop,
                uploaded: uploadedResults
            };
            
        } catch (error) {
            console.error(`处理图片组 ${groupNumber} 时出错:`, error);
            Utils.log('图片上传', `处理组 ${groupNumber} 时出错: ${error.message}`, 'error');
            return {
                success: false,
                message: `处理组 ${groupNumber} 时出错: ${error.message}`
            };
        }
    }
    
    /**
     * 处理单张图片：下载、压缩（可选）、上传
     * @param {string} imageUrl - 图片URL
     * @param {Element} uploadArea - 图片上传区域的容器元素
     * @param {number} groupNumber - 当前组序号
     * @param {number} imageNumber - 当前图片序号
     * @returns {Promise<Object>} - 处理结果 { success: boolean, originalUrl: string, uploadedUrl?: string, message?: string }
     */
    async processSingleImage(imageUrl, uploadArea, groupNumber, imageNumber) {
        let retries = 0;
        const originalUrl = imageUrl;
        Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber}: 开始处理 ${originalUrl}`);
        
        while (retries < this.MAX_RETRIES) {
            if (this.shouldStop) return { success: false, originalUrl, message: '用户中断' };
            
            try {
                // 下载图片为Blob
                const imageBlob = await this.fetchImageAsBlob(originalUrl);
                Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber}: 下载成功 (大小: ${Math.round(imageBlob.size / 1024)} KB)`);
                
                // 压缩图片 (如果需要)
                const processedBlob = await this.compressImageIfNeeded(imageBlob);
                Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber}: 压缩完成 (新大小: ${Math.round(processedBlob.size / 1024)} KB)`);
                
                // 模拟文件选择并上传
                await this.simulateFileSelectAndUpload(processedBlob, uploadArea, originalUrl);
                Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber}: 上传成功`);
                
                // 记录已上传
                this.uploadedImages.add(originalUrl); 
                
                return {
                    success: true,
                    originalUrl
                    // uploadedUrl 可能无法直接获取，取决于网站实现
                };
                
            } catch (error) {
                retries++;
                Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber} 处理失败 (尝试 ${retries}/${this.MAX_RETRIES}): ${error.message}`, 'error');
                
                // 如果不是最后一次重试，等待后重试
                if (retries < this.MAX_RETRIES) {
                    await Utils.delay(this.retryDelay * retries); // 增加重试等待时间
                } else {
                    // 重试次数耗尽
                     Utils.log('图片上传', `组 ${groupNumber} 图片 ${imageNumber} 重试次数耗尽`, 'error');
                    return {
                        success: false,
                        originalUrl,
                        message: `处理失败: ${error.message} (已重试 ${this.MAX_RETRIES} 次)`
                    };
                }
            }
        }
        
        // 理论上不会执行到这里，但作为保险
        return {
            success: false,
            originalUrl,
            message: `处理图片失败，已重试 ${this.MAX_RETRIES} 次`
        };
    }
    
    /**
     * 使用 fetch 下载图片并返回 Blob 对象
     * @param {string} url - 图片URL
     * @returns {Promise<Blob>}
     */
    async fetchImageAsBlob(url) {
        try {
            // 添加时间戳或随机数破坏缓存
            const noCacheUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;
            Utils.log('网络请求', `Fetch URL: ${noCacheUrl}`);
            
            const response = await fetch(noCacheUrl, {
                method: 'GET',
                mode: 'cors', // 尝试允许跨域，但可能受服务器限制
                cache: 'no-cache', // 强制不使用缓存
                referrerPolicy: 'no-referrer' // 尝试避免发送 Referer
            });
            
            if (!response.ok) {
                 // 尝试使用代理或让 background script 下载
                 Utils.log('网络请求', `直接 Fetch 失败 (${response.status})，尝试通过 Background 下载: ${url}`, 'warn');
                 return await this.fetchImageViaBackground(url);
                // throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const blob = await response.blob();
            if (!blob.type.startsWith('image/')) {
                throw new Error('下载的内容不是有效的图片格式');
            }
            return blob;
        } catch (error) {
            Utils.log('网络请求', `直接 Fetch 图片出错: ${error.message}，URL: ${url}，尝试通过 Background 下载`, 'error');
            // 如果直接 fetch 失败 (例如 CORS 问题)，尝试通过 background script 下载
            return await this.fetchImageViaBackground(url);
            // throw new Error(`下载图片失败: ${error.message}`);
        }
    }

    /**
     * 通过 Background Script 下载图片 (用于解决CORS等问题)
     * @param {string} url - 图片URL
     * @returns {Promise<Blob>}
     */
    async fetchImageViaBackground(url) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ type: 'fetchImage', url: url }, response => {
                if (chrome.runtime.lastError) {
                    Utils.log('Background下载', `与Background通信错误: ${chrome.runtime.lastError.message}`, 'error');
                    return reject(new Error(`与Background通信错误: ${chrome.runtime.lastError.message}`));
                }
                
                if (response && response.success && response.dataUrl) {
                    try {
                        // 将 Data URL 转换为 Blob
                        const byteString = atob(response.dataUrl.split(',')[1]);
                        const mimeString = response.dataUrl.split(',')[0].split(':')[1].split(';')[0];
                        const ab = new ArrayBuffer(byteString.length);
                        const ia = new Uint8Array(ab);
                        for (let i = 0; i < byteString.length; i++) {
                            ia[i] = byteString.charCodeAt(i);
                        }
                        const blob = new Blob([ab], { type: mimeString });
                         Utils.log('Background下载', `成功获取 Blob (大小: ${Math.round(blob.size / 1024)} KB)`);
                        resolve(blob);
                    } catch (e) {
                         Utils.log('Background下载', `Data URL 转 Blob 失败: ${e.message}`, 'error');
                         reject(new Error('处理Background返回的图片数据失败'));
                    }
                } else {
                     const errorMsg = response ? response.error : '未知错误';
                     Utils.log('Background下载', `Background下载图片失败: ${errorMsg}`, 'error');
                    reject(new Error(`Background下载图片失败: ${errorMsg}`));
                }
            });
        });
    }
    
    /**
     * 根据配置压缩图片
     * @param {Blob} blob - 原始图片Blob
     * @returns {Promise<Blob>} - 压缩后的图片Blob (或原始Blob如果不需要压缩)
     */
    async compressImageIfNeeded(blob) {
        if (!AppConfig.UPLOAD.ENABLE_COMPRESSION) {
            Utils.log('图片压缩', '压缩功能已禁用');
            return blob;
        }
        
        // 检查文件大小是否需要压缩
        if (blob.size <= AppConfig.UPLOAD.COMPRESSION_THRESHOLD_KB * 1024) {
             Utils.log('图片压缩', `图片大小 (${Math.round(blob.size / 1024)}KB) 小于阈值 ${AppConfig.UPLOAD.COMPRESSION_THRESHOLD_KB}KB，无需压缩`);
             return blob;
        }
        
        Utils.log('图片压缩', `开始压缩图片 (原始大小: ${Math.round(blob.size / 1024)}KB)`);
        
        return new Promise((resolve, reject) => {
            const img = new Image();
            const objectUrl = URL.createObjectURL(blob);
            
            img.onload = () => {
                const maxWidth = AppConfig.UPLOAD.COMPRESSION_MAX_WIDTH;
                const maxHeight = AppConfig.UPLOAD.COMPRESSION_MAX_HEIGHT;
                
                // 计算压缩后尺寸
                let width = img.width;
                let height = img.height;
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width = Math.floor(width * ratio);
                    height = Math.floor(height * ratio);
                }
                
                // 创建Canvas
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                
                // 绘制到Canvas
                ctx.drawImage(img, 0, 0, width, height);
                
                // 从Canvas导出Blob
                canvas.toBlob(compressedBlob => {
                    URL.revokeObjectURL(objectUrl); // 释放内存
                    if (compressedBlob) {
                         Utils.log('图片压缩', `压缩完成 (新大小: ${Math.round(compressedBlob.size / 1024)}KB)`);
                         // 如果压缩后体积反而变大，则返回原图
                         if (compressedBlob.size > blob.size * 1.1) { // 允许10%的误差
                              Utils.log('图片压缩', '压缩后体积增大，返回原图', 'warn');
                              resolve(blob);
                         } else {
                              resolve(compressedBlob);
                         }
                    } else {
                        reject(new Error('Canvas toBlob failed'));
                    }
                }, AppConfig.UPLOAD.COMPRESSION_FORMAT, AppConfig.UPLOAD.COMPRESSION_QUALITY);
            };
            
            img.onerror = (error) => {
                URL.revokeObjectURL(objectUrl);
                console.error('加载图片到Image对象失败:', error);
                reject(new Error('加载图片进行压缩时失败'));
            };
            
            img.src = objectUrl;
        });
    }
    
    /**
     * 模拟文件选择和上传 (网站A特定逻辑)
     * @param {Blob} blob - 图片Blob
     * @param {Element} uploadArea - 上传区域的容器元素
     * @param {string} originalUrl - 原始图片URL，用于生成文件名
     */
    async simulateFileSelectAndUpload(blob, uploadArea, originalUrl) {
        // 1. 查找上传按钮或文件输入框
        const fileInput = uploadArea.querySelector(AppConfig.SELECTORS.SITE_A.UPLOAD_INPUT);
        const uploadButton = uploadArea.querySelector(AppConfig.SELECTORS.SITE_A.UPLOAD_BUTTON);
        
        if (!fileInput) {
            throw new Error('在上传区域内找不到文件输入框 (input[type="file"])');
        }
        
        // 2. 创建 File 对象
        // 从 URL 中提取或生成文件名
        let filename = 'image.jpg';
        try {
            const urlParts = originalUrl.split('/');
            const lastPart = urlParts[urlParts.length - 1];
            // 移除可能的查询参数并确保有扩展名
            filename = lastPart.split('?')[0];
            if (!filename.includes('.')) {
                 filename += '.jpg'; // 默认扩展名
            }
        } catch (e) {
            Utils.log('文件名提取', '从URL提取文件名失败，使用默认名', 'warn');
        }
        
        const file = new File([blob], filename, { type: blob.type });
        
        // 3. 将 File 对象放入文件输入框
        try {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;
             Utils.log('模拟上传', `文件已放入输入框: ${filename}`);
        } catch (e) {
             console.error('将文件放入输入框失败:', e);
             throw new Error('无法模拟文件选择过程');
        }
        
        // 4. 触发 change 事件，模拟用户选择了文件
        fileInput.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
        Utils.log('模拟上传', '已触发文件输入框的 change 事件');
        
        // 5. 等待上传完成 (网站A可能需要特定逻辑来检测)
        await this.waitForUploadComplete(uploadArea, file.name);
        Utils.log('模拟上传', `文件上传完成: ${filename}`);
    }
    
    /**
     * 等待图片上传完成 (网站A特定逻辑)
     * @param {Element} uploadArea - 上传区域容器
     * @param {string} filename - 上传的文件名
     * @returns {Promise<void>}
     */
    async waitForUploadComplete(uploadArea, filename) {
        Utils.log('等待上传', `开始等待 ${filename} 上传完成`);
        const startTime = Date.now();
        const timeout = AppConfig.UPLOAD.UPLOAD_TIMEOUT || 30000; // 30秒超时
        
        return new Promise((resolve, reject) => {
            const checkUpload = () => {
                 if (this.shouldStop) return reject(new Error('用户中断'));
                 
                 // 检查上传成功的标志
                 // 方案1: 查找表示成功的元素 (例如带有图片预览的div)
                 const successIndicator = uploadArea.querySelector(AppConfig.SELECTORS.SITE_A.UPLOAD_SUCCESS_INDICATOR);
                 if (successIndicator) {
                     // 进一步检查预览图片的src是否有效（可选）
                     const imgPreview = successIndicator.querySelector('img');
                     if (imgPreview && imgPreview.src && !imgPreview.src.startsWith('data:')) { // 确保不是本地预览
                         Utils.log('等待上传', `通过成功指示器和有效图片确认 ${filename} 上传完成`);
                         resolve();
                         return;
                     }
                 }
                 
                 // 方案2: 检查上传按钮是否被禁用或隐藏 (某些UI库的做法)
                 // const uploadButton = uploadArea.querySelector(AppConfig.SELECTORS.SITE_A.UPLOAD_BUTTON);
                 // if (uploadButton && (uploadButton.disabled || window.getComputedStyle(uploadButton).display === 'none')) {
                 //     Utils.log('等待上传', `通过按钮状态确认 ${filename} 上传完成`);
                 //     resolve();
                 //     return;
                 // }
                 
                 // 方案3: 检查是否有错误提示
                 const errorIndicator = uploadArea.querySelector(AppConfig.SELECTORS.SITE_A.UPLOAD_ERROR_INDICATOR);
                 if (errorIndicator && window.getComputedStyle(errorIndicator).display !== 'none') {
                     const errorText = errorIndicator.textContent || '未知上传错误';
                     Utils.log('等待上传', `${filename} 上传失败: ${errorText}`, 'error');
                     reject(new Error(`上传失败: ${errorText}`));
                     return;
                 }
                 
                 // 检查是否超时
                 if (Date.now() - startTime > timeout) {
                     Utils.log('等待上传', `${filename} 上传超时`, 'error');
                     reject(new Error('上传超时'));
                     return;
                 }
                 
                 // 继续检查
                 setTimeout(checkUpload, 500); // 每500ms检查一次
            };
            
            checkUpload();
        });
    }
    
    /**
     * 确保网站A的活动已选择 (通常是图文报告)
     * @returns {Promise<boolean>}
     */
    async ensureActivitySelected() {
        Utils.log('活动检查', '检查活动是否已选择');
        try {
            // 使用配置的选择器查找活动选择的元素（例如第一个单选框或复选框）
            const activityInput = await Utils.waitForElement(AppConfig.SELECTORS.SITE_A.ACTIVITY_INPUT, 3000);
            if (activityInput && activityInput.type === 'radio' || activityInput.type === 'checkbox') {
                if (!activityInput.checked) {
                    Utils.log('活动检查', '活动未选择，尝试点击选择');
                    activityInput.click();
                    // 等待可能的动态内容加载
                    await Utils.delay(AppConfig.UPLOAD.ACTIVITY_SELECTION_DELAY || 1000);
                    Utils.log('活动检查', '活动已选择');
                } else {
                     Utils.log('活动检查', '活动已被选择');
                }
                return true;
            } else {
                 Utils.log('活动检查', '未找到活动选择输入框或类型不正确', 'warn');
                 return false; // 或者根据情况抛出错误
            }
        } catch (error) {
            // 如果等待超时或选择器错误
            console.error('选择活动失败:', error);
            Utils.log('活动检查', '查找或选择活动失败', 'error');
            // 根据业务逻辑决定是否抛出错误或允许继续
            if (AppConfig.UPLOAD.REQUIRE_ACTIVITY_SELECTION) {
                 throw new Error('选择活动失败，无法继续上传');
            } else {
                return false;
            }
        }
    }

    /**
     * 添加新的任务组 (网站A特定操作)
     * @param {number} groupNumber - 当前组序号
     * @returns {Promise<Element|null>} - 返回新添加的任务组的上传区域元素，如果失败则返回null
     */
    async addNewGroup(groupNumber) {
        Utils.log('任务组操作', `尝试为第 ${groupNumber} 组添加新任务`);
        
        // 第一组不需要添加，直接查找第一个任务容器
        if (groupNumber === 1) {
            try {
                const firstGroup = await Utils.waitForElement(AppConfig.SELECTORS.SITE_A.TASK_CONTAINER, 5000);
                 if (firstGroup) {
                     Utils.log('任务组操作', '找到第一个任务组容器');
                     return firstGroup; // 返回第一个任务组的容器
                 } else {
                     Utils.log('任务组操作', '未找到第一个任务组容器', 'error');
                     return null;
                 }
            } catch (error) {
                 Utils.log('任务组操作', `查找第一个任务组失败: ${error.message}`, 'error');
                 return null;
            }
        }
        
        // 对于后续组，需要点击"添加"按钮
        try {
            // 查找添加按钮
            const addButton = Utils.findElement(AppConfig.SELECTORS.SITE_A.ADD_TASK_BUTTON);
            if (!addButton) {
                Utils.log('任务组操作', '未找到添加任务按钮', 'error');
                return null;
            }
            
            Utils.log('任务组操作', '点击添加任务按钮');
            addButton.click();
            
            // 等待新任务组出现
            // 新组的选择器通常是基于最后一个任务组 (li[name^="lstPicTask"]:last-child)
            const newGroupSelector = `${AppConfig.SELECTORS.SITE_A.TASK_CONTAINER}:nth-of-type(${groupNumber})`;
            
            Utils.log('任务组操作', `等待新任务组出现: ${newGroupSelector}`);
            
            const newGroupArea = await Utils.waitForElement(newGroupSelector, AppConfig.UPLOAD.NEW_GROUP_TIMEOUT || 10000);
            
            if (newGroupArea) {
                Utils.log('任务组操作', `新任务组 ${groupNumber} 添加成功`);
                // 设置背景色
                this.setGroupBackground(newGroupArea, groupNumber - 1);
                return newGroupArea;
            } else {
                Utils.log('任务组操作', `等待新任务组 ${groupNumber} 超时`, 'error');
                return null;
            }
            
        } catch (error) {
            console.error(`添加新任务组 ${groupNumber} 失败:`, error);
            Utils.log('任务组操作', `添加新组 ${groupNumber} 失败: ${error.message}`, 'error');
            return null;
        }
    }
    
    /**
     * 更新处理状态显示 (需要UI管理器配合)
     * @param {number} current - 当前处理到第几个 (从1开始)
     * @param {number} total - 总共多少个
     * @param {string} statusText - 状态文本
     */
    updateProcessingStatus(current, total, statusText) {
        // 这个方法需要与 UIManager 交互来更新界面
        Utils.log('状态更新', `进度: ${current}/${total} - ${statusText}`);
        
        // 示例：通过发送消息或调用UI管理器的方法来更新
        if (window.uiManager && typeof window.uiManager.updateUploadStatus === 'function') {
            window.uiManager.updateUploadStatus(current, total, statusText);
        } else {
             // 如果没有UI管理器，可以在控制台记录更详细的信息
             console.log(`[上传进度] ${current}/${total} - ${statusText}`);
             // 可以考虑创建一个简单的状态显示元素
             let statusDiv = document.getElementById('image-upload-status-indicator');
             if (!statusDiv) {
                 statusDiv = Utils.createElement('div', {
                     id: 'image-upload-status-indicator',
                     style: AppConfig.STYLES.STATUS_INDICATOR
                 });
                 document.body.appendChild(statusDiv);
             }
             statusDiv.textContent = `图片上传: ${statusText} (${current}/${total})`;
             // 根据状态改变样式
              if (statusText.includes('完成') || statusText.includes('成功')) {
                 statusDiv.style.backgroundColor = '#d4edda'; // 浅绿
                 statusDiv.style.color = '#155724';
                 statusDiv.style.borderColor = '#c3e6cb';
             } else if (statusText.includes('失败') || statusText.includes('错误') || statusText.includes('中断')) {
                 statusDiv.style.backgroundColor = '#f8d7da'; // 浅红
                 statusDiv.style.color = '#721c24';
                 statusDiv.style.borderColor = '#f5c6cb';
             } else {
                 statusDiv.style.backgroundColor = '#cce5ff'; // 浅蓝
                 statusDiv.style.color = '#004085';
                 statusDiv.style.borderColor = '#b8daff';
             }
        }
    }
    
    /**
     * 停止处理
     */
    stopProcessing() {
        if (this.processing) {
            Utils.log('图片上传', '收到停止处理信号');
            this.shouldStop = true;
        }
    }
};
